import { Blocks, BlockType } from '../../docx'
import { TransformContext, getStyleConfig, escapeHtml } from '../context'
import { getInlineStyles } from '../styles'
import { imageCache } from '../image-cache'

// PDF容器宽度常量
const PDF_CONTAINER_WIDTH = 840 // PDF导出时的容器宽度
const PDF_CONTAINER_PADDING = 40 // 容器左右padding
const PDF_CONTENT_WIDTH = PDF_CONTAINER_WIDTH - (PDF_CONTAINER_PADDING * 2) // 实际内容宽度

/**
 * 转换图片块
 */
export function transformImage(block: Blocks, context: TransformContext): string {
  if (block.type !== BlockType.IMAGE) return ''

  const imageData = (block.snapshot as any)?.image
  if (!imageData) return ''

  const { name, caption, width, height, token, scale } = imageData
  const align = (block.snapshot as any)?.align || 'left'
  const alt = extractAltText(caption) || name || '图片'

  // 优先使用缓存中的图片
  const cachedSrc = imageCache.get(token)
  let src = cachedSrc || ''

  // 如果缓存中没有且启用了图片转换，输出警告但不再重复处理
  if (context.options.convertImages !== false && !cachedSrc) {
    console.warn(`⚠️ 图片 ${name} (token: ${token}) 未在缓存中找到，可能预处理失败`)
    // 不再启动异步处理，避免重复下载
  }

  // 如果启用了图片转换，创建图片元素加入上下文
  if (context.options.convertImages !== false) {
    const imgElement = document.createElement('img')
    imgElement.alt = alt
    imgElement.dataset.token = token
    imgElement.dataset.name = name
    if (src) {
      imgElement.src = src
    }
    context.images.push(imgElement)
  }

  // 构建尺寸属性 - 修复比例问题并考虑PDF容器宽度
  const sizeAttrs = buildSizeAttributes(width, height, scale)

  // 构建样式
  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('image', context, inlineStyles.image)

  // 构建对齐样式
  const alignStyle = align === 'center'
    ? 'display: block; margin: 16px auto;'
    : align === 'right'
      ? 'display: block; margin: 16px 0 16px auto;'
      : 'display: block; margin: 16px auto 16px 0;'

  if (context.options.useInlineStyles) {
    const sizeStyle = buildSizeStyle(width, height, scale)

    // 构建完整的内联样式，修复比例问题
    const styleComponents = [
      'max-width: 100%', // 防止图片超出容器宽度
      'height: auto',    // 保持宽高比
      alignStyle.replace(/;$/, ''), // 移除末尾分号
      styleConfig.inlineStyle ? styleConfig.inlineStyle.replace(/;$/, '') : '', // 正确访问inlineStyle
      sizeStyle ? sizeStyle.replace(/;$/, '') : '' // 移除末尾分号
    ].filter(Boolean) // 过滤空字符串

    // 如果有明确的尺寸，使用优化后的尺寸计算，考虑PDF容器宽度
    let finalStyleComponents = styleComponents
    if (width && height && scale) {
      const finalScale = scale || 1
      let scaledWidth = Math.round(width * finalScale)
      let scaledHeight = Math.round(height * finalScale)
      
      // 如果图片宽度超过PDF内容宽度，按比例缩放
      if (scaledWidth > PDF_CONTENT_WIDTH) {
        const ratio = PDF_CONTENT_WIDTH / scaledWidth
        scaledWidth = PDF_CONTENT_WIDTH
        scaledHeight = Math.round(scaledHeight * ratio)
      }
      
      // 对于PDF导出，使用优化后的固定尺寸确保比例正确
      finalStyleComponents = [
        `width: ${scaledWidth}px`,
        `height: ${scaledHeight}px`,
        alignStyle.replace(/;$/, ''),
        styleConfig.inlineStyle ? styleConfig.inlineStyle.replace(/max-width:[^;]*;?|height:[^;]*;?|width:[^;]*;?/g, '').replace(/;$/, '') : ''
      ].filter(Boolean)
    }

    const combinedStyle = finalStyleComponents.join('; ') + ';'
    return `<img src="${escapeHtml(src)}" alt="${escapeHtml(alt)}" style="${combinedStyle}" data-token="${escapeHtml(token)}" data-name="${escapeHtml(name)}"${sizeAttrs} />`
  } else {
    const className = `${context.options.cssClassPrefix || 'feishu'}-image ${context.options.cssClassPrefix || 'feishu'}-image-${align}`
    return `<img src="${escapeHtml(src)}" alt="${escapeHtml(alt)}" class="${className}"${sizeAttrs} data-token="${escapeHtml(token)}" data-name="${escapeHtml(name)}" />`
  }
}

/**
 * 异步处理图片
 */
async function processImageAsync(block: Blocks, token: string): Promise<void> {
  try {
    const dataUrl = await processImageSync(block)
    if (dataUrl) {
      // 更新缓存
      imageCache.set(token, dataUrl)

      // 更新DOM中所有相同token的img元素
      setTimeout(() => {
        const allImgsWithToken = document.querySelectorAll(`img[data-token="${token}"]`)
        allImgsWithToken.forEach(img => {
          if (img instanceof HTMLImageElement) {
            img.src = dataUrl
          }
        })
      }, 0)
    }
  } catch (error) {
    console.error('处理图片失败:', error)
  }
}

/**
 * 同步处理图片
 */
async function processImageSync(block: Blocks): Promise<string | null> {
  try {
    const imageData = (block.snapshot as any)?.image
    if (!imageData?.token) return null

    // 获取图片源
    const sources = await fetchImageSources(block)

    if (!sources?.src) {
      return null
    }

    // 获取图片数据
    const response = await fetch(sources.src)
    if (!response.ok) {
      return null
    }

    const blob = await response.blob()

    // 转换为data URL
    const dataUrl = await blobToDataUrl(blob)
    return dataUrl
  } catch (error) {
    return null
  }
}

/**
 * 获取图片源
 */
async function fetchImageSources(block: Blocks): Promise<{ src: string; originSrc: string } | null> {
  if (block.type !== BlockType.IMAGE) return null

  try {
    const imageManager = (block as any).imageManager
    if (!imageManager?.fetch) return null

    const imageData = (block.snapshot as any)?.image
    if (!imageData?.token) return null

    // 使用imageManager获取图片源
    const sources = await imageManager.fetch(
      { token: imageData.token, isHD: false },
      {},
      (sources: { originSrc: string; src: string }) => sources
    )

    return sources
  } catch (error) {
    return null
  }
}

/**
 * 将Blob转换为data URL
 */
function blobToDataUrl(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = reject
    reader.readAsDataURL(blob)
  })
}

/**
 * 提取图片alt文本
 */
function extractAltText(caption?: any): string {
  if (!caption) return ''

  try {
    // 尝试从caption中提取文本
    const text = caption.text?.initialAttributedTexts?.text?.[0]
    return typeof text === 'string' ? text.trim() : ''
  } catch {
    return ''
  }
}

/**
 * 构建尺寸属性 - 修复比例问题并考虑PDF容器宽度
 */
function buildSizeAttributes(width?: number, height?: number, scale?: number): string {
  const attrs: string[] = []
  const finalScale = scale || 1

  if (width && width > 0) {
    let scaledWidth = Math.round(width * finalScale)
    
    // 如果图片宽度超过PDF内容宽度，限制宽度
    if (scaledWidth > PDF_CONTENT_WIDTH) {
      scaledWidth = PDF_CONTENT_WIDTH
    }
    
    attrs.push(` width="${scaledWidth}"`)
  }

  if (height && height > 0) {
    let scaledHeight = Math.round(height * finalScale)
    
    // 如果设置了宽度限制，按比例调整高度
    if (width && width > 0) {
      const originalWidth = Math.round(width * finalScale)
      if (originalWidth > PDF_CONTENT_WIDTH) {
        const ratio = PDF_CONTENT_WIDTH / originalWidth
        scaledHeight = Math.round(scaledHeight * ratio)
      }
    }
    
    attrs.push(` height="${scaledHeight}"`)
  }

  return attrs.join('')
}

/**
 * 构建尺寸样式 - 修复比例问题并考虑PDF容器宽度
 */
function buildSizeStyle(width?: number, height?: number, scale?: number): string {
  const styles: string[] = []
  const finalScale = scale || 1

  // 只有在有明确尺寸时才设置固定尺寸，并考虑PDF容器宽度限制
  if (width && width > 0 && height && height > 0) {
    let scaledWidth = Math.round(width * finalScale)
    let scaledHeight = Math.round(height * finalScale)
    
    // 如果图片宽度超过PDF内容宽度，按比例缩放
    if (scaledWidth > PDF_CONTENT_WIDTH) {
      const ratio = PDF_CONTENT_WIDTH / scaledWidth
      scaledWidth = PDF_CONTENT_WIDTH
      scaledHeight = Math.round(scaledHeight * ratio)
    }
    
    styles.push(`width: ${scaledWidth}px`)
    styles.push(`height: ${scaledHeight}px`)
  } else if (width && width > 0) {
    // 只有宽度时，设置宽度，高度自动，但限制最大宽度
    let scaledWidth = Math.round(width * finalScale)
    if (scaledWidth > PDF_CONTENT_WIDTH) {
      scaledWidth = PDF_CONTENT_WIDTH
    }
    styles.push(`width: ${scaledWidth}px`)
  } else if (height && height > 0) {
    // 只有高度时，设置高度，宽度自动
    const scaledHeight = Math.round(height * finalScale)
    styles.push(`height: ${scaledHeight}px`)
  }

  return styles.join('; ')
}

/**
 * 构建缩放样式 - 禁用transform缩放，避免比例问题
 */
function buildScaleStyle(scale?: number): string {
  // 禁用transform缩放，统一使用width/height控制尺寸
  return ''
}

/**
 * 构建对齐样式
 */
function buildAlignStyle(align: string): string {
  switch (align) {
    case 'left':
      return 'display: block !important;'
    case 'right':
      return 'display: block !important;'
    case 'center':
      return 'display: block !important; margin-left: auto !important; margin-right: auto !important;'
    default:
      return ''
  }
}

/**
 * 获取对齐CSS类名
 */
function getAlignClass(align: string): string {
  switch (align) {
    case 'left':
      return 'align-left'
    case 'right':
      return 'align-right'
    case 'center':
      return 'align-center'
    default:
      return ''
  }
}

/**
 * 获取容器样式
 */
function getContainerStyle(align: string): string {
  switch (align) {
    case 'left':
      return 'margin: 2px 0; text-align: left;'
    case 'right':
      return 'margin: 2px 0; text-align: right;'
    case 'center':
      return 'margin: 2px 0; text-align: center;'
    default:
      return ''
  }
}

/**
 * 获取容器CSS类名
 */
function getContainerClass(align: string): string {
  switch (align) {
    case 'left':
      return 'image-align-left'
    case 'right':
      return 'image-align-right'
    case 'center':
      return 'image-align-center'
    default:
      return ''
  }
}

