import { Blocks, BlockType } from '../../docx'
import { TransformContext, getStyleConfig, escapeHtml } from '../context'
import { getInlineStyles } from '../styles'
import { convertOperationsToHtml } from '../operations'
import { transformBlock } from '../transform'

/**
 * 转换列表项
 */
export function transformList(block: Blocks, context: TransformContext): string {
  // 获取列表项内容
  const ops = block.zoneState?.content?.ops || []
  const content = convertOperationsToHtml(ops, context)

  // 处理子块（列表项可能包含嵌套的列表或其他类型的块）
  const childrenHtml: string[] = []
  if (block.children && block.children.length > 0) {
    // 使用分组方法，更好地处理混合内容
    const groupedChildren = groupConsecutiveListBlocks(block.children)
    
    for (const group of groupedChildren) {
      if (group.type === 'list' && group.listType) {
        // 处理嵌套列表
        const listHtml = convertNestedListGroup(group.blocks, group.listType, context)
        if (listHtml) {
          childrenHtml.push(listHtml)
        }
      } else {
        // 处理非列表子块（如文本、图片等）
        for (const child of group.blocks) {
          const childHtml = transformBlock(child, context)
          if (childHtml && childHtml.trim()) {
            childrenHtml.push(childHtml)
          }
        }
      }
    }
  }

  const fullContent = content + (childrenHtml.length > 0 ? '\n' + childrenHtml.join('\n') : '')

  if (block.type === BlockType.TODO) {
    return convertTodoItem(block, fullContent, context)
  } else if (block.type === BlockType.ORDERED) {
    return convertOrderedItem(block, fullContent, context)
  } else {
    return convertBulletItem(fullContent, context)
  }
}

/**
 * 转换TODO项
 */
function convertTodoItem(block: Blocks, content: string, context: TransformContext): string {
  if (block.type !== BlockType.TODO) return ''

  const isChecked = block.snapshot && 'done' in block.snapshot ? Boolean(block.snapshot.done) : false
  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('todo', context, inlineStyles['list-item'])

  if (context.options.useInlineStyles) {
    const defaultStyle = 'list-style: none; display: flex; align-items: flex-start;'
    const checkboxStyle = 'margin-right: 8px; margin-top: 2px;'
    const contentStyle = isChecked ? 'text-decoration: line-through; color: #888;' : ''

    const style = styleConfig.inlineStyle || defaultStyle

    return `<li style="${style}">` +
      `<input type="checkbox"${isChecked ? ' checked' : ''} disabled style="${checkboxStyle}">` +
      `<span style="${contentStyle}">${content || ''}</span>` +
      `</li>`
  } else {
    return `<li class="${styleConfig.className}">` +
      `<input type="checkbox"${isChecked ? ' checked' : ''} disabled>` +
      `<span${isChecked ? ' class="checked"' : ''}>${content || ''}</span>` +
      `</li>`
  }
}

/**
 * 转换有序列表项
 */
function convertOrderedItem(block: Blocks, content: string, context: TransformContext): string {
  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('ordered', context, inlineStyles['list-item'])

  if (context.options.useInlineStyles) {
    const defaultStyle = ''
    const style = styleConfig.inlineStyle || defaultStyle
    if (style) {
      return `<li style="${style}">${content || ''}</li>`
    } else {
      return `<li>${content || ''}</li>`
    }
  } else {
    return `<li class="${styleConfig.className}">${content || ''}</li>`
  }
}

/**
 * 转换无序列表项
 */
function convertBulletItem(content: string, context: TransformContext): string {
  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('bullet', context, inlineStyles['list-item'])

  if (context.options.useInlineStyles) {
    const defaultStyle = ''
    const style = styleConfig.inlineStyle || defaultStyle
    if (style) {
      return `<li style="${style}">${content || ''}</li>`
    } else {
      return `<li>${content || ''}</li>`
    }
  } else {
    return `<li class="${styleConfig.className}">${content || ''}</li>`
  }
}

/**
 * 对连续的列表块进行分组
 */
function groupConsecutiveListBlocks(blocks: Blocks[]): Array<{
  type: 'list' | 'other'
  listType?: BlockType
  blocks: Blocks[]
}> {
  const groups: Array<{
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  }> = []

  let currentGroup: {
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  } | null = null

  for (const block of blocks) {
    const isListBlock = block.type === BlockType.BULLET || 
                       block.type === BlockType.ORDERED || 
                       block.type === BlockType.TODO

    if (isListBlock) {
      if (!currentGroup || currentGroup.type !== 'list' || currentGroup.listType !== block.type) {
        // 开始新的列表组
        currentGroup = {
          type: 'list',
          listType: block.type,
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前列表组
        currentGroup.blocks.push(block)
      }
    } else {
      if (!currentGroup || currentGroup.type !== 'other') {
        // 开始新的非列表组
        currentGroup = {
          type: 'other',
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前非列表组
        currentGroup.blocks.push(block)
      }
    }
  }

  return groups
}

/**
 * 转换嵌套列表组
 */
function convertNestedListGroup(listBlocks: Blocks[], listType: BlockType, context: TransformContext): string {
  if (!listBlocks.length) return ''

  const listItems = listBlocks.map(block => transformList(block, context)).filter(Boolean)
  if (!listItems.length) return ''

  const inlineStyles = getInlineStyles()
  const listClassName = listType === BlockType.TODO ? 'todo-list' :
    listType === BlockType.ORDERED ? 'ordered-list' : 'bullet-list'
  const styleConfig = getStyleConfig(listClassName, context, inlineStyles['list'])

  if (context.options.useInlineStyles) {
    if (listType === BlockType.TODO) {
      const style = 'list-style: none; padding-left: 0;'
      return `<ul style="${style}">${listItems.join('')}</ul>`
    } else if (listType === BlockType.ORDERED) {
      // 移除额外样式，保持原生结构
      return `<ol>${listItems.join('')}</ol>`
    } else {
      // 移除额外样式，保持原生结构
      return `<ul>${listItems.join('')}</ul>`
    }
  } else {
    if (listType === BlockType.TODO) {
      return `<ul class="${styleConfig.className}">${listItems.join('')}</ul>`
    } else if (listType === BlockType.ORDERED) {
      return `<ol class="${styleConfig.className}">${listItems.join('')}</ol>`
    } else {
      return `<ul class="${styleConfig.className}">${listItems.join('')}</ul>`
    }
  }
} 