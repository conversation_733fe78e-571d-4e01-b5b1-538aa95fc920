import { Blocks } from '../../docx'
import { TransformContext, getStyleConfig, applyStyle } from '../context'
import { getInlineStyles } from '../styles'
import { convertOperationsToHtml, parseAttributedText } from '../operations'

/**
 * 转换文本块
 */
export function transformText(block: Blocks, context: TransformContext): string {
  // 处理snapshot中的背景色 - 通过上下文传递给operations转换器
  const snapshot = block.snapshot as any
  if (snapshot?.background_color) {
    const backgroundColor = snapshot.background_color

    // 将背景色设置到上下文中，让operations转换器应用到span上
    context.blockBackgroundColor = backgroundColor
  }

  let ops = block.zoneState?.content.ops || []
  
  // 如果没有ops但有attributed text格式的数据，尝试解析
  if (ops.length === 0 && snapshot) {
    if (snapshot.apool && snapshot.initialAttributedTexts) {
      try {
        ops = parseAttributedText({
          apool: snapshot.apool,
          initialAttributedTexts: snapshot.initialAttributedTexts
        })
      } catch (error) {
        console.warn('Failed to parse attributed text in text block:', error)
      }
    }
  }
  
  const content = convertOperationsToHtml(ops, context)

  // 清理上下文中的背景色，避免影响其他块
  if (context.blockBackgroundColor) {
    delete context.blockBackgroundColor
  }

  if (!content.trim()) {
    return '<div></div>'
  }

  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('text', context, inlineStyles.text)

  return applyStyle('div', content, styleConfig, context)
} 