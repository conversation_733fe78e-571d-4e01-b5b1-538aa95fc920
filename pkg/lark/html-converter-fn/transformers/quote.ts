import { Blocks, BlockType } from '../../docx'
import { TransformContext, getStyleConfig, applyStyle } from '../context'
import { getInlineStyles } from '../styles'
import { transformBlock } from '../transform'
import { convertOperationsToHtml } from '../operations'

/**
 * 对连续的列表块进行分组
 */
function groupConsecutiveListBlocks(blocks: Blocks[]): Array<{
  type: 'list' | 'other'
  listType?: BlockType
  blocks: Blocks[]
}> {
  const groups: Array<{
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  }> = []

  let currentGroup: {
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  } | null = null

  for (const block of blocks) {
    const isListBlock = block.type === BlockType.BULLET ||
      block.type === BlockType.ORDERED ||
      block.type === BlockType.TODO

    if (isListBlock) {
      if (!currentGroup || currentGroup.type !== 'list' || currentGroup.listType !== block.type) {
        // 开始新的列表组
        currentGroup = {
          type: 'list',
          listType: block.type,
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前列表组
        currentGroup.blocks.push(block)
      }
    } else {
      if (!currentGroup || currentGroup.type !== 'other') {
        // 开始新的非列表组
        currentGroup = {
          type: 'other',
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前非列表组
        currentGroup.blocks.push(block)
      }
    }
  }

  return groups
}

/**
 * 转换引用块内的列表组
 */
function convertQuoteListGroup(listBlocks: Blocks[], listType: BlockType, context: TransformContext): string {
  if (!listBlocks.length) return ''

  const listItems = listBlocks.map(block => transformBlock(block, context)).filter(Boolean)
  if (!listItems.length) return ''

  const listTag = listType === BlockType.ORDERED ? 'ol' : 'ul'

  if (context.options.useInlineStyles) {
    let listStyle = ''
    switch (listType) {
      case BlockType.TODO:
        listStyle = 'list-style: none; padding-left: 0;'
        break
      case BlockType.ORDERED:
      case BlockType.BULLET:
        listStyle = ''
        break
      default:
        listStyle = ''
    }

    // 移除li::marker颜色和额外样式，保持原生结构
    if (listStyle) {
      return `<${listTag} style="${listStyle}">${listItems.join('')}</${listTag}>`
    } else {
      return `<${listTag}>${listItems.join('')}</${listTag}>`
    }
  } else {
    const className = `${context.options.cssClassPrefix || 'feishu'}-${listType === BlockType.TODO ? 'todo-list' : listType === BlockType.ORDERED ? 'ordered-list' : 'bullet-list'}`
    return `<${listTag} class="${className}">${listItems.join('')}</${listTag}>`
  }
}

/**
 * 转换文本块为p标签（用于引用块内部）
 */
function transformTextAsP(block: Blocks, context: TransformContext): string {
  const ops = block.zoneState?.content.ops || []
  const content = convertOperationsToHtml(ops, context)

  if (!content.trim()) {
    return '<p></p>'
  }

  return `<p>${content}</p>`
}

/**
 * 转换引用块子块
 */
function convertQuoteChildren(children: Blocks[], context: TransformContext): string[] {
  if (!children || children.length === 0) return []

  const childrenHtml: string[] = []

  // 使用分组方法，更好地处理混合内容
  const groupedChildren = groupConsecutiveListBlocks(children)

  for (const group of groupedChildren) {
    if (group.type === 'list' && group.listType) {
      // 处理嵌套列表
      const listHtml = convertQuoteListGroup(group.blocks, group.listType, context)
      if (listHtml) {
        childrenHtml.push(listHtml)
      }
    } else {
      // 处理非列表子块（如文本、图片等）
      for (const child of group.blocks) {
        let childHtml = ''

        // 根据块类型进行特殊处理
        if (child.type === BlockType.TEXT) {
          // 对于文本块，获取其内容并包装在p标签中
          const ops = child.zoneState?.content?.ops || []
          const textContent = convertOperationsToHtml(ops, context)
          if (textContent.trim()) {
            childHtml = `<p>${textContent}</p>`
          }
        } else {
          // 对于其他类型的块，使用标准转换器
          childHtml = transformBlock(child, context)
        }

        if (childHtml && childHtml.trim()) {
          childrenHtml.push(childHtml)
        }
      }
    }
  }

  return childrenHtml
}

/**
 * 转换引用块
 */
export function transformQuote(block: Blocks, context: TransformContext): string {
  let content = ''

  // 首先处理当前块的 zoneState.content.ops（引用块自身的文本内容）
  if (block.zoneState?.content?.ops && block.zoneState.content.ops.length > 0) {
    const opsHtml = convertOperationsToHtml(block.zoneState.content.ops, context)
    if (opsHtml.trim()) {
      // 将引用块自身的文本内容包装在p标签中
      content += `<p>${opsHtml}</p>`
    }
  }

  // 然后处理子块
  const children = block.children || []
  if (children.length > 0) {
    const childrenHtml = convertQuoteChildren(children, context)
    const childrenContent = childrenHtml.join('\n')
    if (childrenContent.trim()) {
      // 如果既有自身内容又有子块内容，用换行分隔
      if (content.trim()) {
        content += '\n' + childrenContent
      } else {
        content = childrenContent
      }
    }
  }

  // 简化处理：对于CALLOUT和QUOTE_CONTAINER都使用blockquote标签
  // 不添加任何装饰性样式，只保留内容的原始格式
  if (!content.trim()) {
    return '<blockquote></blockquote>'
  }

  return `<blockquote>${content}</blockquote>`
} 