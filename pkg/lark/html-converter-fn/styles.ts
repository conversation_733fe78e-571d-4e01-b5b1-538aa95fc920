import { TransformContext } from './context'

/**
 * 生成CSS样式表
 */
export function generateStyles(context: TransformContext): string | undefined {
  if (context.options.useInlineStyles) {
    return undefined
  }

  const cssRules: string[] = []

  // 收集所有样式配置
  context.styleConfigs.forEach((config) => {
    if (config.cssRule) {
      cssRules.push(config.cssRule)
    }
  })

  // 添加默认样式
  cssRules.push(...getDefaultStyles(context.options.cssClassPrefix))

  return cssRules.length > 0 ? cssRules.join('\n') : undefined
}

/**
 * 获取默认样式
 */
function getDefaultStyles(prefix: string): string[] {
  return [
    // 基础样式
    `.${prefix}-text { margin: 10px; }`,
    `.${prefix}-heading-1 { font-size: 2em; font-weight: bold; margin: 0.67em 0; }`,
    `.${prefix}-heading-2 { font-size: 1.5em; font-weight: bold; margin: 0.75em 0; }`,
    `.${prefix}-heading-3 { font-size: 1.17em; font-weight: bold; margin: 0.83em 0; }`,
    `.${prefix}-heading-4 { font-size: 1em; font-weight: bold; margin: 1em 0; }`,
    `.${prefix}-heading-5 { font-size: 0.83em; font-weight: bold; margin: 1.17em 0; }`,
    `.${prefix}-heading-6 { font-size: 0.67em; font-weight: bold; margin: 1.33em 0; }`,

    // 分割线
    `.${prefix}-divider { border: none; border-top: 1px solid #e1e4e8; margin: 16px 0; }`,

    // 代码块
    `.${prefix}-code { background-color: #f6f8fa; border-radius: 6px; padding: 16px; overflow-x: auto; font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace; }`,
    `.${prefix}-code code { background: none; padding: 0; }`,

    // 引用块 - 移除装饰样式，保持简洁
    `.${prefix}-quote { }`,
    `.${prefix}-callout { }`,

    // 列表
    `.${prefix}-list { margin: 16px 0; padding-left: 2em; }`,
    `.${prefix}-list-item { margin: 4px 0; }`,
    `.${prefix}-ordered-list { margin: 16px 0; padding-left: 24px; }`,
    `.${prefix}-ordered-list li::marker { color: #0084ff; }`,
    `.${prefix}-bullet-list { margin: 16px 0; padding-left: 24px; }`,
    `.${prefix}-bullet-list li::marker { color: #0084ff; }`,
    `.${prefix}-todo-list { list-style: none; padding-left: 0; margin: 16px 0; }`,
    `.${prefix}-todo-list li { margin: 4px 0; display: flex; align-items: flex-start; }`,
    `.${prefix}-todo-list li input[type="checkbox"] { margin-right: 8px; margin-top: 2px; }`,
    `.${prefix}-todo-list li .checked { text-decoration: line-through; color: #888; }`,

    // 图片
    `.${prefix}-image { max-width: 100%; height: auto; margin: 8px 0; }`,
    `.${prefix}-image-container { text-align: left; margin: 8px 0; }`,
    `.${prefix}-image-container.center { text-align: center; }`,
    `.${prefix}-image-container.right { text-align: right; }`,

    // 表格
    `.${prefix}-table { border-collapse: collapse; width: 100%; margin: 16px 0; }`,
    `.${prefix}-table th, .${prefix}-table td { border: 1px solid #dfe2e5; padding: 8px 12px; text-align: left; }`,
    `.${prefix}-table th { background-color: #f6f8fa; font-weight: 600; }`,

    // 文件
    `.${prefix}-file { display: inline-flex; align-items: center; padding: 8px 12px; border: 1px solid #dfe2e5; border-radius: 6px; text-decoration: none; color: #0366d6; margin: 4px 0; }`,
    `.${prefix}-file:hover { background-color: #f6f8fa; }`,

    // 白板
    `.${prefix}-whiteboard { border: 1px solid #dfe2e5; border-radius: 6px; margin: 16px 0; overflow: hidden; }`,

    // 内嵌框架
    `.${prefix}-iframe { border: 1px solid #dfe2e5; border-radius: 6px; margin: 16px 0; width: 100%; min-height: 400px; }`,

    // 网格布局
    `.${prefix}-grid { display: flex; flex-wrap: wrap; gap: 16px; margin: 16px 0; align-items: flex-start; }`,
    `.${prefix}-grid-column { flex: 1; min-width: 0; box-sizing: border-box; }`,

    // 目录
    `.${prefix}-toc { border: 1px solid #e9ecef; border-radius: 8px;}`,
    `.${prefix}-toc-title { margin: 0 0 16px 0; font-size: 18px; font-weight: bold; color: #333; }`,
    `.${prefix}-toc-list { margin: 0; padding-left: 0; list-style: none; }`,
    `.${prefix}-toc-list li { margin: 6px 0; line-height: 1.4; list-style: none; }`,
    `.${prefix}-toc-list li::marker { display: none; }`,
    `.${prefix}-toc-list a { color: #646a73; text-decoration: none; padding: 4px 8px; border-radius: 4px; display: inline-block; transition: all 0.2s; }`,
    `.${prefix}-toc-list a:hover { background-color: #f0f0f0; color: #646a73; }`,
    `.${prefix}-toc-list ul { margin: 6px 0 0 0; padding-left: 20px; list-style: none; }`,
    `.${prefix}-toc-list ul li { list-style: none; }`,
    `.${prefix}-toc-list ul li::marker { display: none; }`,
    `@media print {
      .${prefix}-toc { margin: 10px 0 !important; padding: 15px !important; page-break-inside: auto !important; }
      .${prefix}-toc-title { margin: 0 0 10px 0 !important; font-size: 16px !important; page-break-after: avoid !important; page-break-inside: avoid !important; }
      .${prefix}-toc-list { margin: 0 !important; page-break-inside: auto !important; }
      .${prefix}-toc-list > li { margin: 2px 0 !important; line-height: 1.3 !important; page-break-inside: avoid !important; orphans: 2; widows: 2; }
      .${prefix}-toc.long-toc .${prefix}-toc-list > li { margin: 1px 0 !important; line-height: 1.2 !important; }
      .${prefix}-toc-list li li { margin: 1px 0 !important; line-height: 1.3 !important; page-break-inside: avoid !important; }
      .${prefix}-toc-list a { padding: 2px 4px !important; font-size: 13px !important; display: inline !important; }
      .${prefix}-toc-list ul { margin: 2px 0 0 0 !important; padding-left: 16px !important; page-break-inside: auto !important; }
    }`,

    // 未知类型
    `.${prefix}-unknown { padding: 8px; border: 1px dashed #dfe2e5; border-radius: 4px; background-color: #fff5b4; color: #735c0f; margin: 8px 0; }`,
    `.${prefix}-unknown::before { content: "未知块类型: " attr(data-type) " - "; font-weight: bold; }`
  ]
}

/**
 * 获取内联样式
 */
export function getInlineStyles(): Record<string, string> {
  return {
    text: 'margin: 10px; line-height: 1.6;',
    'heading-1': 'font-size: 26px; font-weight: 500; margin: 0.67em 0;',
    'heading-2': 'font-size: 22px; font-weight: 500; margin: 0.75em 0;',
    'heading-3': 'font-size: 18px; font-weight: 500; margin: 0.83em 0;',
    'heading-4': 'font-size: 16px; font-weight: 500; margin: 1em 0;',
    'heading-5': 'font-size: 16px; font-weight: 500; margin: 1.17em 0;',
    'heading-6': 'font-size: 16px; font-weight: 500; margin: 1.33em 0;',
    divider: 'border: none; border-top: 1px solid #e1e4e8; margin: 16px 0;',
    code: 'background-color: #f6f8fa; border-radius: 6px; padding: 16px; overflow-x: auto; font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;',
    quote: '',
    callout: '',
    list: 'margin: 16px 0; padding-left: 2em;',
    'list-item': 'margin: 4px 0;',
    'ordered-list': 'margin: 16px 0; padding-left: 24px;',
    'bullet-list': 'margin: 16px 0; padding-left: 24px;',
    'todo-list': 'list-style: none; padding-left: 0; margin: 16px 0;',
    'todo-item': 'margin: 4px 0; display: flex; align-items: flex-start;',
    'todo-checkbox': 'margin-right: 8px; margin-top: 2px;',
    'todo-checked': 'text-decoration: line-through; color: #888;',
    image: 'margin: 8px 0;', // 移除max-width和height限制，让具体尺寸控制比例
    'image-container': 'text-align: left; margin: 8px 0;',
    'image-container-center': 'text-align: center; margin: 8px 0;',
    'image-container-right': 'text-align: right; margin: 8px 0;',
    table: 'border-collapse: collapse; width: 100%; margin: 16px 0;',
    'table-cell': 'border: 1px solid #dfe2e5; padding: 8px 12px; text-align: left;',
    'table-header': 'border: 1px solid #dfe2e5; padding: 8px 12px; text-align: left; background-color: #f6f8fa; font-weight: 600;',
    file: 'display: inline-flex; align-items: center; padding: 8px 12px; border: 1px solid #dfe2e5; border-radius: 6px; text-decoration: none; color: #0366d6; margin: 4px 0;',
    whiteboard: 'border: 1px solid #dfe2e5; border-radius: 6px; margin: 16px 0; overflow: hidden;',
    iframe: 'border: 1px solid #dfe2e5; border-radius: 6px; margin: 16px 0; width: 100%; min-height: 400px;',
    grid: 'display: flex; flex-wrap: wrap; gap: 16px; margin: 16px 0; align-items: flex-start;',
    'grid-column': 'flex: 1; min-width: 0; box-sizing: border-box;',
    toc: '  border: 1px solid #e9ecef; border-radius: 8px;   margin: 20px 0;',
    'toc-title': 'margin: 0 0 16px 0; font-size: 18px; font-weight: bold; color: #333;',
    'toc-list': 'margin: 0; padding-left: 0; list-style: none;',
    'toc-item': 'margin: 6px 0; line-height: 1.4;',
    'toc-link': 'color: #646a73; text-decoration: none; padding: 4px 8px; border-radius: 4px; display: inline-block; transition: all 0.2s;',
    unknown: 'padding: 8px; border: 1px dashed #dfe2e5; border-radius: 4px; background-color: #fff5b4; color: #735c0f; margin: 8px 0;'
  }
} 