import { Operation, Attributes } from '../docx'
import { TransformContext, escapeHtml } from './context'

/**
 * 解析attributed text格式
 */
export function parseAttributedText(data: {
  apool: {
    nextNum: number
    numToAttrib: Record<string, [string, string]>
  }
  initialAttributedTexts: {
    attribs: Record<string, string>
    text: Record<string, string>
  }
}): Operation[] {
  const { apool, initialAttributedTexts } = data
  const { numToAttrib } = apool
  const { attribs, text } = initialAttributedTexts

  const operations: Operation[] = []

  // 解析每个文本段
  for (const [key, textContent] of Object.entries(text)) {
    const attribString = attribs[key]
    
    if (!textContent) continue

    // 解析属性字符串，格式如 "*0*1+1"
    const attributes: Attributes = {}
    
    if (attribString) {
      // 匹配属性模式，如 *0*1+1 表示从位置0到位置1应用属性0和1
      const attribMatches = attribString.match(/\*(\d+)/g)
      
      if (attribMatches) {
        for (const match of attribMatches) {
          const attribNum = match.substring(1) // 去掉*前缀
          const attribDef = numToAttrib[attribNum]
          
          if (attribDef && Array.isArray(attribDef) && attribDef.length === 2) {
            const [attrName, attrValue] = attribDef
            attributes[attrName] = attrValue
          }
        }
      }
    }

    operations.push({
      insert: textContent,
      attributes: Object.keys(attributes).length > 0 ? attributes : undefined
    })
  }

  return operations
}

/**
 * 转换操作列表为HTML
 */
export function convertOperationsToHtml(ops: Operation[] = [], context: TransformContext): string {
  if (!ops.length) return ''

  return ops.map(op => convertOperationToHtml(op, context)).join('')
}

/**
 * 转换单个操作为HTML
 */
export function convertOperationToHtml(op: Operation, context: TransformContext): string {
  const { attributes, insert } = op

  if (!insert) return ''

  // 处理内联组件
  if (attributes?.['inline-component']) {
    return convertInlineComponent(op, context)
  }

  // 处理特殊字符转义
  let content = escapeHtml(insert)

  // 处理内联代码
  if (attributes?.inlineCode) {
    return `<code>${content}</code>`
  }

  // 处理数学公式
  if (attributes?.equation) {
    const equation = attributes.equation.replace(/\n$/, '')
    // 只输出原始 LaTeX，交给前端渲染
    return `<span class="equation" data-equation="${escapeHtml(equation)}">${escapeHtml(equation)}</span>`
  }

  // 应用文本格式
  content = applyTextFormats(content, attributes, context)

  return content
}

/**
 * 转换内联组件
 */
function convertInlineComponent(op: Operation, context: TransformContext): string {
  const { attributes, insert } = op

  try {
    const inlineComponent = JSON.parse(attributes?.['inline-component'] || '{}')
    
    if (inlineComponent.type === 'mention_doc') {
      const { data } = inlineComponent
      const title = data.title || insert || '文档链接'
      const url = data.raw_url || ''
      
      // 获取图标
      let emoji = '📄' // 默认文档图标
      if (data.icon_info?.key) {
        try {
          emoji = String.fromCodePoint(parseInt(data.icon_info.key, 16))
        } catch {
          emoji = '📄'
        }
      }
      
      if (url) {
        return `<a href="${escapeHtml(url)}" target="_blank" rel="noopener noreferrer" class="inline-doc-link">${emoji} ${escapeHtml(title)}</a>`
      } else {
        return `<span class="inline-doc-mention">${emoji} ${escapeHtml(title)}</span>`
      }
    } else if (inlineComponent.type === 'reminder') {
      const { data } = inlineComponent
      const notifyTime = data.notify_time ? new Date(data.notify_time).toLocaleString('zh-CN') : '未知时间'
      return `<span class="inline-reminder">⏰ ${escapeHtml(notifyTime)}</span>`
    } else {
      // 其他未知类型的内联组件，返回原始文本
      return escapeHtml(insert)
    }
  } catch (error) {
    console.warn('Failed to parse inline-component:', error)
    // 解析失败时返回原始文本
    return escapeHtml(insert)
  }
}

/**
 * 应用文本格式
 */
function applyTextFormats(content: string, attributes?: Attributes, context?: TransformContext): string {
  if (!attributes) return content

  let html = content

  // 粗体
  if (attributes.bold) {
    html = `<strong>${html}</strong>`
  }

  // 斜体
  if (attributes.italic) {
    html = `<em>${html}</em>`
  }

  // 删除线
  if (attributes.strikethrough) {
    html = `<del>${html}</del>`
  }

  // 下划线
  if ((attributes as any).underline) {
    html = `<u>${html}</u>`
  }

  // 链接
  if (attributes.link) {
    const url = decodeURIComponent(attributes.link)
    html = `<a href="${escapeHtml(url)}">${html}</a>`
  }

  // 颜色和背景色处理
  const textHighlightBackground = attributes.textHighlightBackground as string
  const textHighlight = attributes.textHighlight as string
  const htmlAttrs = attributes as any
  const contextBackgroundColor = context?.blockBackgroundColor

  if (htmlAttrs.color || htmlAttrs.backgroundColor || textHighlightBackground || textHighlight || htmlAttrs.fontSize || contextBackgroundColor) {
    const styles: string[] = []

    if (htmlAttrs.color) {
      styles.push(`color: ${htmlAttrs.color}`)
    }

    // 支持 textHighlight 属性作为文字颜色
    if (textHighlight) {
      styles.push(`color: ${textHighlight}`)
    }

    if (htmlAttrs.backgroundColor) {
      styles.push(`background-color: ${htmlAttrs.backgroundColor}`)
    }

    // 支持 textHighlightBackground 属性作为背景色
    if (textHighlightBackground) {
      styles.push(`background-color: ${textHighlightBackground}`)
    }

    // 支持从上下文传递的块背景色
    if (contextBackgroundColor && !htmlAttrs.backgroundColor && !textHighlightBackground) {
      styles.push(`background-color: ${contextBackgroundColor}`)
    }

    if (htmlAttrs.fontSize) {
      styles.push(`font-size: ${htmlAttrs.fontSize}`)
    }

    if (styles.length > 0) {
      html = `<span style="${styles.join('; ')}">${html}</span>`
    }
  }

  return html
} 