import { Blocks } from '../docx'

// 转换选项
export interface TransformOptions {
  /** 是否使用内联样式 */
  useInlineStyles: boolean
  /** CSS类名前缀 */
  cssClassPrefix: string
  /** 是否转换图片 */
  convertImages: boolean
  /** 是否转换文件 */
  convertFiles: boolean
  /** 自定义样式配置 */
  customStyles: Record<string, string>
  /** 是否生成目录 */
  generateToc: boolean
  /** 目录标题 */
  tocTitle: string
  /** 目录最大层级 */
  tocMaxLevel: number
}

// 样式配置
export interface StyleConfig {
  className: string
  inlineStyle?: string
  cssRule?: string
}

// 目录项
export interface TocItem {
  /** 标题文本 */
  text: string
  /** 标题层级 (1-6) */
  level: number
  /** 锚点ID */
  anchor: string
  /** 子目录项 */
  children: TocItem[]
}

// 转换结果
export interface TransformResult {
  html: string
  images: HTMLImageElement[]
  files: HTMLAnchorElement[]
  styles?: string
  /** 目录结构 */
  toc: TocItem[]
  /** 带目录的完整HTML */
  htmlWithToc?: string
}

// 转换上下文
export interface TransformContext {
  options: TransformOptions
  images: HTMLImageElement[]
  files: HTMLAnchorElement[]
  styleConfigs: Map<string, StyleConfig>
  /** 当前块的背景色（用于传递给operations转换器） */
  blockBackgroundColor?: string
}

// 块转换器函数类型
export type BlockTransformer = (block: Blocks, context: TransformContext) => string

/**
 * 创建转换上下文
 */
export function createTransformContext(options: Partial<TransformOptions> = {}): TransformContext {
  const defaultOptions: TransformOptions = {
    useInlineStyles: true,
    cssClassPrefix: 'feishu',
    convertImages: true,
    convertFiles: true,
    customStyles: {},
    generateToc: true,
    tocTitle: '目录',
    tocMaxLevel: 6
  }

  return {
    options: { ...defaultOptions, ...options },
    images: [],
    files: [],
    styleConfigs: new Map()
  }
}

/**
 * 获取样式配置
 */
export function getStyleConfig(
  type: string,
  context: TransformContext,
  defaultStyle?: string
): StyleConfig {
  const key = `${context.options.cssClassPrefix}-${type}`

  if (context.styleConfigs.has(key)) {
    return context.styleConfigs.get(key)!
  }

  const className = key
  const customStyle = context.options.customStyles[type]
  const inlineStyle = customStyle || defaultStyle

  const config: StyleConfig = {
    className,
    inlineStyle,
    cssRule: inlineStyle ? `.${className} { ${inlineStyle} }` : undefined
  }

  context.styleConfigs.set(key, config)
  return config
}

/**
 * 应用样式到元素
 */
export function applyStyle(
  tag: string,
  content: string,
  styleConfig: StyleConfig,
  context: TransformContext,
  additionalAttrs: string = ''
): string {
  if (context.options.useInlineStyles && styleConfig.inlineStyle) {
    return `<${tag} style="${styleConfig.inlineStyle}"${additionalAttrs}>${content}</${tag}>`
  } else {
    return `<${tag} class="${styleConfig.className}"${additionalAttrs}>${content}</${tag}>`
  }
}

/**
 * HTML转义
 */
export function escapeHtml(text: string): string {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
} 