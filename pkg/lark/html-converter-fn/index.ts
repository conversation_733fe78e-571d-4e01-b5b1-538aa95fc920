import { PageBlock, Blocks, BlockType } from '../docx'
import { createTransformContext, TransformContext } from './context'
import { transformBlock } from './transform'
import { generateStyles } from './styles'
import { preprocessImages } from './image-processor'
import { generateToc, generateHtmlWithToc } from './toc-generator'

// 导出类型
export type {
  TransformContext,
  TransformOptions,
  TransformResult,
  StyleConfig,
  BlockTransformer,
  TocItem
} from './context'

// 导出转换器
export * from './transformers'

// 导出目录生成器
export { generateToc, generateHtmlWithToc, addAnchorToHeadings } from './toc-generator'

/**
 * 扁平化处理块的children，类似Markdown转换的逻辑
 * 对于heading块，将其children提升到与heading同级处理
 */
function flattenBlocks(blocks: Blocks[]): Blocks[] {
  return blocks
    .map(block => {
      // 对于网格块，扁平化其列内容
      if (block.type === BlockType.GRID) {
        return flattenBlocks(
          block.children.map(column => column.children).flat(1)
        )
      }

      // 对于标题块，将标题本身和其children都提升到同级
      if (
        block.type === BlockType.HEADING1 ||
        block.type === BlockType.HEADING2 ||
        block.type === BlockType.HEADING3 ||
        block.type === BlockType.HEADING4 ||
        block.type === BlockType.HEADING5 ||
        block.type === BlockType.HEADING6 ||
        block.type === BlockType.HEADING7 ||
        block.type === BlockType.HEADING8 ||
        block.type === BlockType.HEADING9
      ) {
        return [block, ...flattenBlocks(block.children)]
      }

      // 对于同步源块，只扁平化其children
      if (block.type === BlockType.SYNCED_SOURCE) {
        return flattenBlocks(block.children)
      }

      // 其他块直接返回
      return block
    })
    .flat(1)
}

/**
 * 对连续的列表块进行分组
 */
function groupConsecutiveListBlocks(blocks: Blocks[]): Array<{
  type: 'list' | 'other'
  listType?: BlockType
  blocks: Blocks[]
}> {
  const groups: Array<{
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  }> = []

  let currentGroup: {
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  } | null = null

  for (const block of blocks) {
    const isListBlock = block.type === BlockType.BULLET ||
      block.type === BlockType.ORDERED ||
      block.type === BlockType.TODO

    if (isListBlock) {
      if (!currentGroup || currentGroup.type !== 'list' || currentGroup.listType !== block.type) {
        // 开始新的列表组
        currentGroup = {
          type: 'list',
          listType: block.type,
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前列表组
        currentGroup.blocks.push(block)
      }
    } else {
      if (!currentGroup || currentGroup.type !== 'other') {
        // 开始新的非列表组
        currentGroup = {
          type: 'other',
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前非列表组
        currentGroup.blocks.push(block)
      }
    }
  }

  return groups
}

/**
 * 转换顶级列表组
 */
function convertTopLevelListGroup(listBlocks: Blocks[], listType: BlockType, context: TransformContext): string {
  if (!listBlocks.length) return ''

  const listItems = listBlocks.map(block => transformBlock(block, context)).filter(Boolean)
  if (!listItems.length) return ''

  const listTag = listType === BlockType.ORDERED ? 'ol' : 'ul'

  if (context.options.useInlineStyles) {
    let listStyle = ''
    switch (listType) {
      case BlockType.TODO:
        listStyle = 'list-style: none; padding-left: 0;'
        break
      case BlockType.ORDERED:
      case BlockType.BULLET:
        listStyle = ''
        break
      default:
        listStyle = ''
    }

    // 移除li::marker颜色和额外样式，保持原生结构
    if (listStyle) {
      return `<${listTag} style="${listStyle}">${listItems.join('')}</${listTag}>`
    } else {
      return `<${listTag}>${listItems.join('')}</${listTag}>`
    }
  } else {
    const className = `${context.options.cssClassPrefix || 'feishu'}-${listType === BlockType.TODO ? 'todo-list' : listType === BlockType.ORDERED ? 'ordered-list' : 'bullet-list'}`
    return `<${listTag} class="${className}">${listItems.join('')}</${listTag}>`
  }
}

/**
 * 递归收集所有块的类型信息
 */
function collectAllBlockTypes(blocks: Blocks[], depth = 0): { type: string, depth: number, id?: number, hasChildren: boolean, textContent?: string, extraInfo?: any }[] {
  const result: { type: string, depth: number, id?: number, hasChildren: boolean, textContent?: string, extraInfo?: any }[] = []

  blocks.forEach((block, index) => {
    let extraInfo: any = undefined

    // 为不同类型的块收集特殊信息
    switch (block.type) {
      case 'view':
        extraInfo = {
          viewInfo: '文件容器块',
          childrenTypes: block.children?.map(child => child.type) || [],
          recordId: block.record?.id
        }
        break
      case 'file':
        const fileSnapshot = (block.snapshot as any)?.file
        extraInfo = {
          fileName: fileSnapshot?.name || '未知文件名',
          fileToken: fileSnapshot?.token || '无token',
          recordId: block.record?.id
        }
        break
      case 'image':
        const imageSnapshot = (block.snapshot as any)?.image
        extraInfo = {
          imageName: imageSnapshot?.name || '未知图片名',
          imageToken: imageSnapshot?.token || '无token',
          width: imageSnapshot?.width,
          height: imageSnapshot?.height,
          mimeType: imageSnapshot?.mimeType
        }
        break
      case 'table':
        const tableSnapshot = block.snapshot as any
        extraInfo = {
          rows: tableSnapshot?.rows_id?.length || 0,
          columns: tableSnapshot?.columns_id?.length || 0
        }
        break
      case 'code':
        const codeBlock = block as any
        extraInfo = {
          language: codeBlock?.language || '未知语言'
        }
        break
      case 'callout':
        extraInfo = {
          calloutInfo: '标注/提示块'
        }
        break
      case 'iframe':
        const iframeSnapshot = (block.snapshot as any)?.iframe
        extraInfo = {
          url: iframeSnapshot?.component?.url || '无URL',
          height: iframeSnapshot?.height || 0
        }
        break
      case 'whiteboard':
        const whiteboardSnapshot = block.snapshot as any
        extraInfo = {
          whiteboardInfo: '白板块',
          hasWhiteboardBlock: !!(block as any).whiteboardBlock
        }
        break
      default:
        // 对于其他类型，如果有特殊的snapshot数据也显示
        if ((block.snapshot as any) && Object.keys(block.snapshot).length > 1) {
          extraInfo = {
            snapshotKeys: Object.keys(block.snapshot).filter(key => key !== 'type')
          }
        }
        break
    }

    const info = {
      type: block.type,
      depth,
      id: block.id,
      hasChildren: block.children && block.children.length > 0,
      textContent: block.zoneState?.allText || undefined,
      extraInfo
    }
    result.push(info)

    // 递归处理子块
    if (block.children && block.children.length > 0) {
      const childResults = collectAllBlockTypes(block.children, depth + 1)
      result.push(...childResults)
    }
  })

  return result
}

/**
 * 打印所有块类型的调试信息
 */
function printBlockTypesDebugInfo(rootBlock: PageBlock): void {
  console.error('🔍 === PDF导出 - 检查View块 ===')

  if (!rootBlock.children || rootBlock.children.length === 0) {
    console.error('⚠️ rootBlock没有子块')
    return
  }

  // 收集所有块类型
  const allBlocks = collectAllBlockTypes(rootBlock.children)

  // 只关注view块
  const viewBlocks = allBlocks.filter(block => block.type === 'view')

  if (viewBlocks.length === 0) {
    console.error('ℹ️ 没有发现view块')
    return
  }

  console.error(`📁 发现 ${viewBlocks.length} 个view块:`)

  viewBlocks.forEach((viewBlock, index) => {
    console.error(`\n📁 View块 #${index + 1}:`)
    console.error('  基本信息:', {
      id: viewBlock.id,
      depth: viewBlock.depth,
      hasChildren: viewBlock.hasChildren,
      textContent: viewBlock.textContent || '无文本内容'
    })

    if (viewBlock.extraInfo) {
      console.error('  详细信息:', viewBlock.extraInfo)
    }

    // 查找该view块的子块
    const childBlocks = allBlocks.filter(block =>
      block.depth === viewBlock.depth + 1 &&
      allBlocks.indexOf(block) > allBlocks.indexOf(viewBlock)
    )

    if (childBlocks.length > 0) {
      console.error('  子块详情:')
      childBlocks.forEach((child, childIndex) => {
        const childInfo = `    ${childIndex + 1}. ${child.type}`
        const childText = child.textContent ? ` - "${child.textContent.slice(0, 50)}${child.textContent.length > 50 ? '...' : ''}"` : ''
        let childExtra = ''

        if (child.extraInfo) {
          switch (child.type) {
            case 'file':
              childExtra = ` [文件: ${child.extraInfo.fileName}]`
              break
            case 'image':
              childExtra = ` [图片: ${child.extraInfo.imageName}]`
              break
            default:
              if (child.extraInfo.snapshotKeys) {
                childExtra = ` [属性: ${child.extraInfo.snapshotKeys.join(', ')}]`
              }
              break
          }
        }

        console.error(childInfo + childText + childExtra)

        // 如果子块有额外信息，也打印出来
        if (child.extraInfo && child.type === 'file') {
          console.error('      文件详情:', {
            fileName: child.extraInfo.fileName,
            token: child.extraInfo.fileToken,
            recordId: child.extraInfo.recordId
          })
        }
      })
    } else {
      console.error('  ⚠️ 没有找到子块')
    }
  })

  console.error('🔍 === View块检查完成 ===')
}

/**
 * 将 docx.rootBlock 转换为 HTML
 * 
 * @param rootBlock 飞书文档的根块
 * @param options 转换选项
 * @returns HTML转换结果
 */
export function convertDocxToHtml(
  rootBlock: PageBlock | null,
  options: Partial<import('./context').TransformOptions> = {}
): import('./context').TransformResult {
  if (!rootBlock) {
    return {
      html: '',
      images: [],
      files: [],
      styles: undefined,
      toc: [],
      htmlWithToc: ''
    }
  }

  const context = createTransformContext(options)
  const htmlBlocks: string[] = []

  // 生成目录
  const toc = generateToc(rootBlock, context)

  // 先扁平化所有块，确保heading的children被正确处理
  const flattenedBlocks = flattenBlocks(rootBlock.children)

  // 对扁平化后的块进行列表分组处理
  const groupedBlocks = groupConsecutiveListBlocks(flattenedBlocks)

  for (const group of groupedBlocks) {
    if (group.type === 'list' && group.listType) {
      // 处理顶级列表组
      const listHtml = convertTopLevelListGroup(group.blocks, group.listType, context)
      if (listHtml) {
        htmlBlocks.push(listHtml)
      }
    } else {
      // 处理单个非列表块
      for (const child of group.blocks) {
        const html = transformBlock(child, context)
        if (html.trim()) {
          htmlBlocks.push(html)
        }
      }
    }
  }

  const html = htmlBlocks.join('\n')
  const styles = generateStyles(context)
  const htmlWithToc = generateHtmlWithToc(html, toc, context)

  return {
    html,
    images: context.images,
    files: context.files,
    styles,
    toc,
    htmlWithToc
  }
}

/**
 * 将 docx.rootBlock 转换为 HTML，并预处理图片为 data URL
 * 
 * @param rootBlock 飞书文档的根块
 * @param options 转换选项
 * @returns HTML转换结果（图片已转换为data URL）
 */
export async function convertDocxToHtmlWithImages(
  rootBlock: PageBlock | null,
  options: Partial<import('./context').TransformOptions> = {}
): Promise<import('./context').TransformResult> {
  if (!rootBlock) {
    return {
      html: '',
      images: [],
      files: [],
      styles: undefined,
      toc: [],
      htmlWithToc: ''
    }
  }

  // 预处理图片
  await preprocessImages(rootBlock)

  // 根据选项决定是否下载文件
  let fileMap = new Map<string, Blob>()
  if (options.convertFiles !== false) {
    fileMap = await processFilesForPdf(rootBlock)
  }

  // 执行转换（convertDocxToHtml内部已经使用了扁平化处理）
  const result = convertDocxToHtml(rootBlock, options)

  // 如果有下载的文件，将其注入到HTML中
  if (fileMap.size > 0) {
    result.html = injectFileDataUrls(result.html, fileMap)
  }

  return result
}

/**
 * PDF专用的高效转换函数，优化性能
 * 
 * @param rootBlock 飞书文档的根块
 * @param options 转换选项
 * @returns HTML转换结果（图片已转换为data URL）
 */
export async function convertDocxToHtmlForPdf(
  rootBlock: PageBlock | null,
  options: Partial<import('./context').TransformOptions> = {}
): Promise<import('./context').TransformResult> {
  if (!rootBlock) {
    return {
      html: '',
      images: [],
      files: [],
      styles: undefined,
      toc: [],
      htmlWithToc: ''
    }
  }
  console.error('🔍 === PDF导出 - 开始转换 ===',rootBlock)
  // 预处理图片
  await preprocessImages(rootBlock)

  // 获取文件URL（不下载，PDF专用）
  const fileUrlMap = processFileUrlsForPdf(rootBlock)

  // 创建转换上下文
  const context = createTransformContext(options)
  const htmlBlocks: string[] = []

  // 生成目录
  const toc = generateToc(rootBlock, context)

  // 先扁平化所有块，确保heading的children被正确处理
  const flattenedBlocks = flattenBlocks(rootBlock.children)

  // 保留列表分组逻辑，确保列表正确渲染
  const groupedBlocks = groupConsecutiveListBlocks(flattenedBlocks)

  for (const group of groupedBlocks) {
    if (group.type === 'list' && group.listType) {
      // 处理顶级列表组
      const listHtml = convertTopLevelListGroup(group.blocks, group.listType, context)
      if (listHtml) {
        htmlBlocks.push(listHtml)
      }
    } else {
      // 处理单个非列表块
      for (const child of group.blocks) {
        const html = transformBlock(child, context)
        if (html.trim()) {
          htmlBlocks.push(html)
        }
      }
    }
  }

  let html = htmlBlocks.join('\n')

  // 如果有文件URL，将其注入到HTML中
  if (fileUrlMap.size > 0) {
    html = injectFileUrlsForPdf(html, fileUrlMap)
  }

  const styles = generateStyles(context)
  const htmlWithToc = generateHtmlWithToc(html, toc, context)

  return {
    html,
    images: context.images,
    files: context.files,
    styles,
    toc,
    htmlWithToc
  }
}

/**
 * 下载单个文件
 */
async function downloadFileForPdf(
  token: string,
  fileName: string,
  recordId: string,
  options: {
    signal?: AbortSignal
    onProgress?: (progress: number) => void
  } = {}
): Promise<Blob | null> {
  const { signal, onProgress } = options

  try {
    console.error(`📎 开始下载文件: ${fileName}`)

    // 尝试从DOM中查找实际的视频URL
    const videoElement = document.querySelector(`video[src*="${token}"]`) as HTMLVideoElement
    let actualUrl: string | null = null

    if (videoElement?.src) {
      actualUrl = videoElement.src
      console.error(`🎬 从DOM找到视频URL: ${actualUrl}`)
    }

    // 使用飞书的视频/文件下载URL，参考DOM中的实际路径
    const isVideoFile = /\.(mp4|avi|mov|mkv|wmv|flv|webm)$/i.test(fileName)

    const urlsToTry: string[] = []

    if (actualUrl) {
      // 优先使用从DOM获取的实际URL
      urlsToTry.push(actualUrl)
    }

    if (isVideoFile) {
      // 视频文件的各种可能URL格式
      urlsToTry.push(
        `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/video/${token}/?quality=1080p&mount_point=docx_file`,
        `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/video/${token}/?quality=720p&mount_point=docx_file`,
        `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/video/${token}/?mount_point=docx_file`
      )
    } else {
      // 其他文件的URL格式
      urlsToTry.push(
        `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/all/${token}/?mount_point=docx_file`,
        `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/file/${token}/?mount_point=docx_file`
      )
    }

    console.error(`📝 文件类型: ${isVideoFile ? '视频文件' : '普通文件'}`)
    console.error(`🔗 尝试 ${urlsToTry.length} 个URL...`)

    // 依次尝试所有URL
    for (let i = 0; i < urlsToTry.length; i++) {
      const downloadUrl = urlsToTry[i]
      console.error(`📡 尝试URL ${i + 1}/${urlsToTry.length}: ${downloadUrl}`)

      try {
        const response = await fetch(downloadUrl, {
          method: 'GET',
          credentials: 'include',
          signal
        })

        if (response.ok) {
          console.error(`✅ URL ${i + 1} 成功!`)
          return await processResponse(response, fileName, onProgress)
        } else {
          console.error(`❌ URL ${i + 1} 失败: ${response.status} ${response.statusText}`)
        }
      } catch (urlError) {
        console.error(`❌ URL ${i + 1} 错误:`, urlError)
      }
    }

    console.error(`❌ 所有URL都失败了`)
    return null
  } catch (error) {
    console.error(`❌ 下载文件失败: ${fileName}`, error)
    return null
  }
}

/**
 * 处理响应并返回Blob
 */
async function processResponse(
  response: Response,
  fileName: string,
  onProgress?: (progress: number) => void
): Promise<Blob> {
  const contentLength = response.headers.get('content-length')
  const total = contentLength ? parseInt(contentLength, 10) : 0

  console.error(`📦 文件大小: ${total > 0 ? (total / 1024 / 1024).toFixed(2) + 'MB' : '未知'}`)

  if (!response.body) {
    throw new Error('响应体为空')
  }

  // 读取流并显示进度
  const reader = response.body.getReader()
  const chunks: Uint8Array[] = []
  let receivedLength = 0

  while (true) {
    const { done, value } = await reader.read()

    if (done) break

    chunks.push(value)
    receivedLength += value.length

    if (total > 0 && onProgress) {
      const progress = receivedLength / total
      onProgress(progress)
      console.error(`📊 下载进度: ${Math.round(progress * 100)}%`)
    }
  }

  // 合并所有chunks
  const allChunks = new Uint8Array(receivedLength)
  let position = 0
  for (const chunk of chunks) {
    allChunks.set(chunk, position)
    position += chunk.length
  }

  const blob = new Blob([allChunks])
  console.error(`✅ 文件下载完成: ${fileName}, 实际大小: ${(blob.size / 1024 / 1024).toFixed(2)}MB`)

  return blob
}

/**
 * 收集所有文件块并下载
 */
async function processFilesForPdf(rootBlock: PageBlock): Promise<Map<string, Blob>> {
  const fileMap = new Map<string, Blob>()

  // 收集所有文件块
  const allBlocks = collectAllBlockTypes(rootBlock.children)
  const fileBlocks = allBlocks.filter(block => block.type === 'file')

  if (fileBlocks.length === 0) {
    console.error('ℹ️ 没有发现文件块')
    return fileMap
  }

  console.error(`📁 发现 ${fileBlocks.length} 个文件块，开始下载...`)

  // 并行下载所有文件
  const downloadPromises = fileBlocks.map(async (fileBlock, index) => {
    try {
      if (!fileBlock.extraInfo) {
        console.error(`⚠️ 文件块 ${index + 1} 缺少详细信息`)
        return
      }

      const { fileName, fileToken, recordId } = fileBlock.extraInfo

      console.error(`📎 开始下载文件 ${index + 1}/${fileBlocks.length}: ${fileName}`)

      const blob = await downloadFileForPdf(fileToken, fileName, recordId, {
        onProgress: (progress) => {
          // console.error(`📊 文件 "${fileName}" 下载进度: ${Math.round(progress * 100)}%`)
        }
      })

      if (blob) {
        fileMap.set(fileToken, blob)
        console.error(`✅ 文件下载成功: ${fileName}`)
      } else {
        console.error(`❌ 文件下载失败: ${fileName}`)
      }
    } catch (error) {
      console.error(`❌ 下载文件时出错:`, error)
    }
  })

  await Promise.all(downloadPromises)

  console.error(`📁 文件下载完成，成功下载 ${fileMap.size}/${fileBlocks.length} 个文件`)
  return fileMap
}

/**
 * 将下载的文件转换为data URL并更新HTML
 */
function injectFileDataUrls(html: string, fileMap: Map<string, Blob>): string {
  if (fileMap.size === 0) return html

  console.error('🔄 开始将文件转换为data URL并注入HTML...')

  let updatedHtml = html

  // 查找所有文件链接并替换为data URL
  const fileRegex = /data-token="([^"]+)"[^>]*data-name="([^"]+)"/g
  let match

  while ((match = fileRegex.exec(html)) !== null) {
    const [fullMatch, token, fileName] = match
    const blob = fileMap.get(token)

    if (blob) {
      // 创建data URL
      const dataUrl = URL.createObjectURL(blob)

      // 构建下载链接
      const downloadLink = `href="${dataUrl}" download="${fileName}"`

      // 替换原来的 href="#"
      updatedHtml = updatedHtml.replace(
        new RegExp(`href="#"([^>]*${token}[^>]*${fileName}[^>]*)`, 'g'),
        `${downloadLink}$1`
      )

      console.error(`🔗 文件链接已更新: ${fileName} -> data URL`)
    }
  }

  console.error('✅ 文件data URL注入完成')
  return updatedHtml
}

/**
 * 为PDF导出获取文件URL（不下载）
 */
function getFileUrlForPdf(token: string, fileName: string): string {
  // 尝试从DOM中查找实际的视频URL
  const videoElement = document.querySelector(`video[src*="${token}"]`) as HTMLVideoElement

  if (videoElement?.src) {
    console.error(`🎬 从DOM找到视频URL: ${videoElement.src}`)
    return videoElement.src
  }

  // 检查文件类型决定URL格式
  const isVideoFile = /\.(mp4|avi|mov|mkv|wmv|flv|webm)$/i.test(fileName)

  if (isVideoFile) {
    // 视频文件使用video路径
    return `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/video/${token}/?quality=1080p&mount_point=docx_file`
  } else {
    // 其他文件使用all路径
    return `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/all/${token}/?mount_point=docx_file`
  }
}

/**
 * 为PDF收集文件URL（不下载文件）
 */
function processFileUrlsForPdf(rootBlock: PageBlock): Map<string, string> {
  const fileUrlMap = new Map<string, string>()

  // 收集所有文件块
  const allBlocks = collectAllBlockTypes(rootBlock.children)
  const fileBlocks = allBlocks.filter(block => block.type === 'file')

  if (fileBlocks.length === 0) {
    console.error('ℹ️ 没有发现文件块')
    return fileUrlMap
  }

  console.error(`📁 发现 ${fileBlocks.length} 个文件块，获取URL...`)

  fileBlocks.forEach((fileBlock, index) => {
    if (!fileBlock.extraInfo) {
      console.error(`⚠️ 文件块 ${index + 1} 缺少详细信息`)
      return
    }

    const { fileName, fileToken } = fileBlock.extraInfo
    const fileUrl = getFileUrlForPdf(fileToken, fileName)

    fileUrlMap.set(fileToken, fileUrl)
    console.error(`🔗 文件URL获取: ${fileName} -> ${fileUrl}`)
  })

  console.error(`📁 文件URL收集完成，共 ${fileUrlMap.size} 个文件`)
  return fileUrlMap
}

/**
 * 将文件URL注入HTML（PDF用）
 */
function injectFileUrlsForPdf(html: string, fileUrlMap: Map<string, string>): string {
  if (fileUrlMap.size === 0) return html

  console.error('🔄 开始将文件URL注入HTML...')

  let updatedHtml = html

  // 查找所有文件链接并替换为实际URL
  const fileRegex = /data-token="([^"]+)"[^>]*data-name="([^"]+)"/g
  let match

  while ((match = fileRegex.exec(html)) !== null) {
    const [fullMatch, token, fileName] = match
    const fileUrl = fileUrlMap.get(token)

    if (fileUrl) {
      // 替换原来的 href="#"
      updatedHtml = updatedHtml.replace(
        new RegExp(`href="#"([^>]*${token}[^>]*${fileName}[^>]*)`, 'g'),
        `href="${fileUrl}" target="_blank"$1`
      )

      console.error(`🔗 文件链接已更新: ${fileName} -> ${fileUrl}`)
    }
  }

  console.error('✅ 文件URL注入完成')
  return updatedHtml
} 