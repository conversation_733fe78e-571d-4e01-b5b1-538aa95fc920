const BASE_PATHNAME = '/api/box/stream/download/all/'
const VIDEO_PATHNAME = '/api/box/stream/download/video/'

/**
 * @description Resolve file download link.
 */
export const resolveFileDownloadUrl = ({
  token,
  recordId,
}: {
  token: string
  recordId: string
}): string => {
  // 使用新的预览下载接口
  const url = `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/preview/${token}?mount_point=docx_file&preview_type=16`
  console.error(`📁 使用新的下载URL格式: ${url}`)
  return url
}

/**
 * @description Resolve video file download link (for video files like mp4, avi, etc.)
 */
export const resolveVideoDownloadUrl = ({
  token,
  recordId,
}: {
  token: string
  recordId: string
}): string => {
  // 使用新的预览下载接口（视频文件也使用相同格式）
  const url = `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/preview/${token}?mount_point=docx_file&preview_type=16`
  console.error(`📁 使用新的视频下载URL格式: ${url}`)
  return url
}

/**
 * @description Check if a file is a video file based on extension
 */
export const isVideoFile = (fileName: string): boolean => {
  return /\.(mp4|avi|mov|mkv|wmv|flv|webm|m4v|3gp|ogv)$/i.test(fileName)
}
