import { BlockConverter, ConversionContext } from '../base-converter'
import { BlockType, Blocks } from '../../docx'
import { imageCache } from './image-cache'

export class ImageConverter extends BlockConverter {
  canHandle(blockType: BlockType): boolean {
    return blockType === BlockType.IMAGE
  }

  convert(block: Blocks, context: ConversionContext): string {
    if (block.type !== BlockType.IMAGE) return ''

    const imageData = block.snapshot?.image
    if (!imageData) return ''

    const { name, caption, width, height, token, scale } = imageData
    // 获取对齐方式，使用类型断言处理扩展字段
    const align = (block.snapshot as any)?.align || 'left'
    const alt = this.extractAltText(caption) || name || '图片'

    // 检查缓存中是否已有这张图片
    const cachedSrc = imageCache.get(token)
    let src = cachedSrc || ''

    // 如果启用了图片转换且缓存中没有，则开始处理
    if (context.options.convertImages !== false && !cachedSrc) {
      // 创建图片对象，模拟markdown AST中的image结构
      const imageObj = {
        type: 'image',
        url: '',
        alt,
        data: {
          name,
          token,
          fetchSources: () => this.fetchImageSources(block)
        }
      }

      const imgElement = document.createElement('img')
      imgElement.alt = alt
      imgElement.dataset.token = token
      imgElement.dataset.name = name

      // 异步处理图片，并更新缓存和DOM
      this.processImageAsync(imageObj, token).catch(error => {
        console.error('图片处理失败:', error)
      })

      context.images.push(imgElement)
    }

    const className = this.getClassName('image', context)
    const customStyle = this.getCustomStyle('image', context)

    if (context.options.useInlineStyles !== false) {
      const sizeStyle = this.buildSizeStyle(width, height, scale)
      const alignStyle = this.buildAlignStyle(align)
      const scaleStyle = this.buildScaleStyle(scale)
      const defaultStyle = 'max-width: 100%; height: auto; margin: 2px 0;'

      // 合并所有样式，确保对齐样式不被覆盖
      let combinedStyle = [defaultStyle, sizeStyle, alignStyle, scaleStyle].filter(Boolean).join(' ')
      if (customStyle) {
        combinedStyle = `${customStyle} ${alignStyle} ${scaleStyle}`.trim()
      }

      const style = ` style="${combinedStyle}"`
      const imgTag = `<img src="${src}" alt="${this.escapeHtml(alt)}" data-token="${this.escapeHtml(token)}" data-name="${this.escapeHtml(name)}" data-align="${this.escapeHtml(align)}" data-scale="${this.escapeHtml(scale?.toString() || '1')}"${style}>`

      // 使用容器包装处理对齐
      const containerStyle = this.getContainerStyle(align)
      if (containerStyle) {
        return `<div style="${containerStyle}">${imgTag}</div>`
      }

      return imgTag
    } else {
      const sizeAttrs = this.buildSizeAttributes(width, height, scale)
      const alignClass = this.getAlignClass(align)
      const finalClassName = alignClass ? `${className} ${alignClass}` : className
      const imgTag = `<img src="${src}" alt="${this.escapeHtml(alt)}" data-token="${this.escapeHtml(token)}" data-name="${this.escapeHtml(name)}" data-align="${this.escapeHtml(align)}" data-scale="${this.escapeHtml(scale?.toString() || '1')}" class="${finalClassName}"${sizeAttrs}>`

      // 使用容器包装处理对齐
      const containerClass = this.getContainerClass(align)
      if (containerClass) {
        return `<div class="${containerClass}">${imgTag}</div>`
      }

      return imgTag
    }
  }

  /**
   * 获取所有图片的data URL（供外部调用）
   */
  static async processAllImages(tokens: string[], blocks: Map<string, Blocks>): Promise<Map<string, string>> {
    const results = new Map<string, string>()

    console.error(`开始处理 ${tokens.length} 张图片...`)

    // 并行处理所有图片
    const promises = tokens.map(async (token, index) => {
      try {
        const block = blocks.get(token)
        if (!block || block.type !== BlockType.IMAGE) return

        const converter = new ImageConverter()
        const imageObj = {
          type: 'image',
          url: '',
          alt: '',
          data: {
            name: block.snapshot?.image?.name || '',
            token,
            fetchSources: () => converter.fetchImageSources(block)
          }
        }

        const dataUrl = await converter.processImageSync(imageObj)
        if (dataUrl) {
          results.set(token, dataUrl)
          imageCache.set(token, dataUrl)
          console.error(`图片 ${index + 1}/${tokens.length} 处理完成: ${imageObj.data.name}`)
        }
      } catch (error) {
        console.error(`图片 ${index + 1}/${tokens.length} 处理失败:`, error)
      }
    })

    await Promise.all(promises)
    console.error(`所有图片处理完成，成功处理 ${results.size}/${tokens.length} 张图片`)

    return results
  }

  private async fetchImageSources(block: Blocks): Promise<{ src: string; originSrc: string } | null> {
    if (block.type !== BlockType.IMAGE) return null

    try {
      const imageManager = block.imageManager
      if (!imageManager?.fetch) return null

      const imageData = block.snapshot?.image
      if (!imageData?.token) return null

      // 使用imageManager获取图片源
      const sources = await imageManager.fetch(
        { token: imageData.token, isHD: false },
        {},
        (sources: { originSrc: string; src: string }) => sources
      )

      return sources
    } catch (error) {
      console.error('获取图片源失败:', error)
      return null
    }
  }

  private async processImageAsync(imageObj: any, token: string): Promise<void> {
    try {
      const dataUrl = await this.processImageSync(imageObj)
      if (dataUrl) {
        // 更新缓存
        imageCache.set(token, dataUrl)

        // 更新DOM中所有相同token的img元素
        setTimeout(() => {
          const allImgsWithToken = document.querySelectorAll(`img[data-token="${token}"]`)
          allImgsWithToken.forEach(img => {
            if (img instanceof HTMLImageElement) {
              img.src = dataUrl
            }
          })
        }, 0)
      }
    } catch (error) {
      console.error('处理图片失败:', error)
    }
  }

  private async processImageSync(imageObj: any): Promise<string | null> {
    try {
      if (!imageObj.data?.fetchSources) {
        console.error('❌ 图片对象没有fetchSources方法:', imageObj)
        return null
      }

      console.error('🔄 开始转换图片为dataURL:', imageObj.data.name || 'unknown')

      // 获取图片源
      const sources = await imageObj.data.fetchSources()
      console.error('📡 获取到图片源:', sources)

      if (!sources?.src) {
        console.error('❌ 无法获取图片源URL:', sources)
        return null
      }

      console.error('🌐 正在下载图片:', sources.src)

      // 获取图片数据
      const response = await fetch(sources.src)
      if (!response.ok) {
        console.error('❌ 图片下载失败:', response.status, response.statusText)
        return null
      }

      const blob = await response.blob()
      console.error('📦 图片下载成功，大小:', blob.size, 'bytes, 类型:', blob.type)

      // 转换为data URL
      const dataUrl = await this.blobToDataUrl(blob)

      console.error('✅ 图片转换为dataURL成功:', imageObj.data.name || 'unknown', 'dataURL长度:', dataUrl.length)
      return dataUrl
    } catch (error) {
      console.error('❌ 处理图片失败:', imageObj.data.name || 'unknown', error)
      return null
    }
  }

  private blobToDataUrl(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  private extractAltText(caption?: any): string {
    if (!caption) return ''

    try {
      // 尝试从caption中提取文本
      const text = caption.text?.initialAttributedTexts?.text?.[0]
      return typeof text === 'string' ? text.trim() : ''
    } catch {
      return ''
    }
  }

  private buildSizeStyle(width?: number, height?: number, scale?: number): string {
    const styles: string[] = []
    const finalScale = scale || 1

    // 只有在有明确尺寸时才设置固定尺寸，确保比例正确
    if (width && width > 0 && height && height > 0) {
      const scaledWidth = Math.round(width * finalScale)
      const scaledHeight = Math.round(height * finalScale)
      styles.push(`width: ${scaledWidth}px`)
      styles.push(`height: ${scaledHeight}px`)
    } else if (width && width > 0) {
      // 只有宽度时，设置宽度，高度自动
      const scaledWidth = Math.round(width * finalScale)
      styles.push(`width: ${scaledWidth}px`)
    } else if (height && height > 0) {
      // 只有高度时，设置高度，宽度自动
      const scaledHeight = Math.round(height * finalScale)
      styles.push(`height: ${scaledHeight}px`)
    }

    return styles.join('; ')
  }

  private buildSizeAttributes(width?: number, height?: number, scale?: number): string {
    const attrs: string[] = []
    const finalScale = scale || 1

    if (width && width > 0) {
      const scaledWidth = Math.round(width * finalScale)
      attrs.push(` width="${scaledWidth}"`)
    }

    if (height && height > 0) {
      const scaledHeight = Math.round(height * finalScale)
      attrs.push(` height="${scaledHeight}"`)
    }

    return attrs.join('')
  }

  private buildScaleStyle(scale?: number): string {
    // 禁用transform缩放，统一使用width/height控制尺寸，避免比例问题
    return ''
  }

  private buildAlignStyle(align: string): string {
    switch (align) {
      case 'left':
        return 'display: block !important;'
      case 'right':
        return 'display: block !important;'
      case 'center':
        return 'display: block !important; margin-left: auto !important; margin-right: auto !important;'
      default:
        return ''
    }
  }

  private getAlignClass(align: string): string {
    switch (align) {
      case 'left':
        return 'align-left'
      case 'right':
        return 'align-right'
      case 'center':
        return 'align-center'
      default:
        return ''
    }
  }

  private getContainerStyle(align: string): string {
    switch (align) {
      case 'left':
        return 'margin: 2px 0; text-align: left;'
      case 'right':
        return 'margin: 2px 0; text-align: right;'
      case 'center':
        return 'margin: 2px 0; text-align: center;'
      default:
        return ''
    }
  }

  private getContainerClass(align: string): string {
    switch (align) {
      case 'left':
        return 'image-align-left'
      case 'right':
        return 'image-align-right'
      case 'center':
        return 'image-align-center'
      default:
        return ''
    }
  }
} 