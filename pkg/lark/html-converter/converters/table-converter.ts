import { BlockConverter, ConversionContext } from '../base-converter'
import { BlockType, Blocks } from '../../docx'

export class TableConverter extends BlockConverter {
  canHandle(blockType: BlockType): boolean {
    return blockType === BlockType.TABLE || blockType === BlockType.CELL
  }

  convert(block: Blocks, context: ConversionContext): string {
    if (block.type === BlockType.TABLE) {
      return this.convertTable(block, context)
    } else if (block.type === BlockType.CELL) {
      return this.convertCell(block, context)
    }
    return ''
  }

  private convertTable(block: Blocks, context: ConversionContext): string {
    if (block.type !== BlockType.TABLE) return ''

    const { columns_id, rows_id } = block.snapshot as any
    const columnCount = columns_id?.length || 0
    const rowCount = rows_id?.length || 0

    if (columnCount === 0 || rowCount === 0) {
      return '<table><tr><td>空表格</td></tr></table>'
    }

    // 将子块按行分组
    const cellBlocks = block.children.filter(child => child.type === BlockType.CELL)
    const rows: Blocks[][] = []

    for (let i = 0; i < rowCount; i++) {
      const rowCells = cellBlocks.slice(i * columnCount, (i + 1) * columnCount)
      rows.push(rowCells)
    }

    const tableRows = rows.map((rowCells, rowIndex) => {
      const cells = rowCells.map(cell => this.convertCell(cell, context)).join('')
      return `<tr>${cells}</tr>`
    }).join('\n')

    const className = this.getClassName('table', context)
    const customStyle = this.getCustomStyle('table', context)

    if (context.options.useInlineStyles !== false) {
      const defaultStyle = 'border-collapse: collapse; width: 100%; margin: 16px 0; border: 1px solid #ddd;'
      const style = customStyle ? ` style="${customStyle}"` : ` style="${defaultStyle}"`
      return `<table${style}>\n${tableRows}\n</table>`
    } else {
      return `<table class="${className}">\n${tableRows}\n</table>`
    }
  }

  private convertCell(block: Blocks, context: ConversionContext): string {
    if (block.type !== BlockType.CELL) return ''

    // 使用基类提供的通用子块处理方法
    const childrenHtml = this.processChildren(block.children || [], context)

    // 如果没有子块内容，尝试从当前块提取文本
    if (childrenHtml.length === 0) {
      const ops = block.zoneState?.content?.ops || []
      const content = this.convertOperationsToHtml(ops, context)
      if (content.trim()) {
        childrenHtml.push(content)
      }
    }

    const content = childrenHtml.join('') || '&nbsp;'

    const className = this.getClassName('cell', context)
    const customStyle = this.getCustomStyle('cell', context)

    if (context.options.useInlineStyles !== false) {
      const defaultStyle = 'border: 1px solid #ddd; padding: 8px; vertical-align: top;'
      const style = customStyle ? ` style="${customStyle}"` : ` style="${defaultStyle}"`
      return `<td${style}>${content}</td>`
    } else {
      return `<td class="${className}">${content}</td>`
    }
  }

  /**
   * 处理表格单元格内的子块，支持列表分组和任意类型的子块
   */
  private processChildren(children: Blocks[], context: ConversionContext): string[] {
    if (!children || children.length === 0) {
      return []
    }

    const childrenHtml: string[] = []
    const groupedChildren = this.groupConsecutiveListBlocks(children)
    
    for (const group of groupedChildren) {
      if (group.type === 'list' && group.listType) {
        // 处理列表组 - 表格中的列表使用紧凑样式
        const listHtml = this.convertTableListGroup(group.blocks, group.listType, context)
        if (listHtml) {
          childrenHtml.push(listHtml)
        }
      } else {
        // 处理单个非列表块
        for (const child of group.blocks) {
          const childHtml = this.convertChildBlock(child, context)
          if (childHtml && childHtml.trim()) {
            // 为表格单元格内容添加适当的包装
            const wrappedHtml = this.wrapCellContent(childHtml, child.type)
            childrenHtml.push(wrappedHtml)
          }
        }
      }
    }

    return childrenHtml
  }

  /**
   * 转换表格中的列表组（使用紧凑样式）
   */
  private convertTableListGroup(listBlocks: Blocks[], listType: BlockType, context: ConversionContext): string {
    if (!listBlocks.length) return ''

    const listItems = listBlocks.map(block => this.convertChildBlock(block, context)).filter(Boolean)
    if (!listItems.length) return ''

    // 表格中的列表使用更紧凑的样式
    let listStyle = ''
    const listTag = listType === BlockType.ORDERED ? 'ol' : 'ul'

    if (context.options.useInlineStyles !== false) {
      switch (listType) {
        case BlockType.TODO:
          listStyle = 'list-style: none; padding-left: 0;'
          break
        case BlockType.ORDERED:
        case BlockType.BULLET:
          listStyle = 'padding-left: 16px;'
          break
        default:
          listStyle = 'padding-left: 16px;'
      }
    }

    // 为有序列表和无序列表添加标记颜色样式
    if (context.options.useInlineStyles !== false && (listType === BlockType.ORDERED || listType === BlockType.BULLET)) {
      const markerStyle = 'li::marker { color: #0084ff; }'
      return `<style>${markerStyle}</style><${listTag} style="${listStyle}">${listItems.join('')}</${listTag}>`
    }

    return `<${listTag} style="${listStyle}">${listItems.join('')}</${listTag}>`
  }

  /**
   * 为表格单元格内容添加适当的包装
   */
  private wrapCellContent(content: string, blockType: BlockType): string {
    // 文本类型现在使用div，不需要特殊处理margin
    if (blockType === BlockType.TEXT ||
        blockType === BlockType.HEADING7 ||
        blockType === BlockType.HEADING8 ||
        blockType === BlockType.HEADING9) {
      return content
    }

    // 标题类型保持原样
    if (blockType === BlockType.HEADING1 ||
        blockType === BlockType.HEADING2 ||
        blockType === BlockType.HEADING3 ||
        blockType === BlockType.HEADING4 ||
        blockType === BlockType.HEADING5 ||
        blockType === BlockType.HEADING6) {
      return content
    }

    // 其他类型直接返回
    return content
  }
} 