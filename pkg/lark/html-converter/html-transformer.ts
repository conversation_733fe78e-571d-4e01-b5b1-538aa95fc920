import { PageBlock, Blocks, BlockType } from '../docx'
import {
  BlockConverter,
  ConversionContext,
  HtmlTransformerOptions,
  HtmlTransformResult,
  BlockTransformer
} from './base-converter'
import {
  TextConverter,
  HeadingConverter,
  DividerConverter,
  CodeConverter,
  QuoteConverter,
  ListConverter,
  ImageConverter,
  TableConverter,
  FileConverter,
  WhiteboardConverter,
  IframeConverter,
  GridConverter
} from './converters'

export class HtmlTransformer implements BlockTransformer {
  private converters = new Map<BlockType, BlockConverter>()

  constructor() {
    this.registerDefaultConverters()
  }

  /**
   * 注册默认转换器
   */
  private registerDefaultConverters(): void {
    const textConverter = new TextConverter()
    const headingConverter = new HeadingConverter()
    const dividerConverter = new DividerConverter()
    const codeConverter = new CodeConverter()
    const quoteConverter = new QuoteConverter()
    const listConverter = new ListConverter()
    const imageConverter = new ImageConverter()
    const tableConverter = new TableConverter()
    const fileConverter = new FileConverter()
    const whiteboardConverter = new WhiteboardConverter()
    const iframeConverter = new IframeConverter()
    const gridConverter = new GridConverter()

    // 注册文本转换器
    this.registerConverter(BlockType.TEXT, textConverter)
    this.registerConverter(BlockType.HEADING7, textConverter)
    this.registerConverter(BlockType.HEADING8, textConverter)
    this.registerConverter(BlockType.HEADING9, textConverter)

    // 注册标题转换器
    this.registerConverter(BlockType.HEADING1, headingConverter)
    this.registerConverter(BlockType.HEADING2, headingConverter)
    this.registerConverter(BlockType.HEADING3, headingConverter)
    this.registerConverter(BlockType.HEADING4, headingConverter)
    this.registerConverter(BlockType.HEADING5, headingConverter)
    this.registerConverter(BlockType.HEADING6, headingConverter)

    // 注册分割线转换器
    this.registerConverter(BlockType.DIVIDER, dividerConverter)

    // 注册代码块转换器
    this.registerConverter(BlockType.CODE, codeConverter)

    // 注册引用块转换器
    this.registerConverter(BlockType.QUOTE_CONTAINER, quoteConverter)
    this.registerConverter(BlockType.CALLOUT, quoteConverter)

    // 注册列表转换器
    this.registerConverter(BlockType.BULLET, listConverter)
    this.registerConverter(BlockType.ORDERED, listConverter)
    this.registerConverter(BlockType.TODO, listConverter)

    // 注册图片转换器
    this.registerConverter(BlockType.IMAGE, imageConverter)

    // 注册表格转换器
    this.registerConverter(BlockType.TABLE, tableConverter)
    this.registerConverter(BlockType.CELL, tableConverter)

    // 注册文件转换器
    this.registerConverter(BlockType.FILE, fileConverter)

    // 注册白板转换器
    this.registerConverter(BlockType.WHITEBOARD, whiteboardConverter)

    // 注册内嵌框架转换器
    this.registerConverter(BlockType.IFRAME, iframeConverter)

    // 注册网格布局转换器
    this.registerConverter(BlockType.GRID, gridConverter)
    this.registerConverter(BlockType.GRID_COLUMN, gridConverter)
  }

  /**
   * 注册转换器
   */
  registerConverter(blockType: BlockType, converter: BlockConverter): void {
    this.converters.set(blockType, converter)
  }

  /**
   * 移除转换器
   */
  unregisterConverter(blockType: BlockType): void {
    this.converters.delete(blockType)
  }

  /**
   * 转换根块为HTML
   */
  transform(rootBlock: PageBlock, options: HtmlTransformerOptions = {}): HtmlTransformResult {
    const context: ConversionContext = {
      images: [],
      files: [],
      options: {
        useInlineStyles: true,
        cssClassPrefix: 'feishu',
        convertImages: true,
        convertFiles: true,
        ...options
      },
      transformer: this
    }

    const htmlBlocks: string[] = []

    // 对顶级块进行列表分组处理
    const groupedBlocks = this.groupConsecutiveListBlocks(rootBlock.children)

    for (const group of groupedBlocks) {
      if (group.type === 'list' && group.listType) {
        // 处理顶级列表组
        const listHtml = this.convertTopLevelListGroup(group.blocks, group.listType, context)
        if (listHtml) {
          htmlBlocks.push(listHtml)
        }
      } else {
        // 处理单个非列表块
        for (const child of group.blocks) {
          const html = this.convertBlock(child, context)
          if (html) {
            htmlBlocks.push(html)
          }
        }
      }
    }

    const html = htmlBlocks.join('\n')

    return {
      html,
      images: context.images,
      files: context.files,
      styles: this.generateStyles(context),
      toc: [],
      htmlWithToc: html
    }
  }

  async transformWithImages(rootBlock: PageBlock, options: HtmlTransformerOptions = {}): Promise<HtmlTransformResult> {
    // 先预处理所有图片
    await this.preprocessImages(rootBlock)

    const context: ConversionContext = {
      images: [],
      files: [],
      options: {
        useInlineStyles: true,
        cssClassPrefix: 'feishu',
        convertImages: true,
        convertFiles: true,
        ...options
      },
      transformer: this
    }

    const htmlBlocks: string[] = []

    // 对顶级块进行列表分组处理
    const groupedBlocks = this.groupConsecutiveListBlocks(rootBlock.children)

    for (const group of groupedBlocks) {
      if (group.type === 'list' && group.listType) {
        // 处理顶级列表组
        const listHtml = this.convertTopLevelListGroup(group.blocks, group.listType, context)
        if (listHtml) {
          htmlBlocks.push(listHtml)
        }
      } else {
        // 处理单个非列表块
        for (const child of group.blocks) {
          const html = this.convertBlock(child, context)
          if (html) {
            htmlBlocks.push(html)
          }
        }
      }
    }

    const html = htmlBlocks.join('\n')

    return {
      html,
      images: context.images,
      files: context.files,
      styles: this.generateStyles(context),
      toc: [],
      htmlWithToc: html
    }
  }

  /**
 * 预处理所有图片，将它们转换为data URL
 */
  private async preprocessImages(rootBlock: PageBlock): Promise<void> {

    // 收集所有图片块
    const imageTokens: string[] = []
    const imageBlocks = new Map<string, Blocks>()

    this.collectImageBlocks(rootBlock, imageTokens, imageBlocks)



    if (imageTokens.length === 0) {
      return
    }

    // 使用ImageConverter的静态方法处理所有图片
    await ImageConverter.processAllImages(imageTokens, imageBlocks)
  }

  /**
 * 递归收集所有图片块
 */
  private collectImageBlocks(block: PageBlock | Blocks, tokens: string[], blocks: Map<string, Blocks>): void {
    console.error('🔍 检查块类型:', block.type)

    if (block.type === BlockType.IMAGE) {
      const imageData = block.snapshot?.image
      console.error('🖼️ 发现图片块:', imageData)

      if (imageData?.token) {
        console.error('📝 收集图片token:', imageData.token)
        tokens.push(imageData.token)
        blocks.set(imageData.token, block as Blocks)
      } else {
        console.warn('⚠️ 图片块没有token:', block)
      }
    }

    // 递归处理子块
    if ('children' in block && block.children) {
      console.error(`📁 处理 ${block.children.length} 个子块`)
      for (const child of block.children) {
        this.collectImageBlocks(child, tokens, blocks)
      }
    }
  }

  /**
   * 实现BlockTransformer接口
   * 提供给转换器使用的通用子块转换方法
   */
  convertBlock(block: Blocks, context: ConversionContext): string {
    const converter = this.converters.get(block.type)

    if (converter && converter.canHandle(block.type)) {
      try {
        return converter.convert(block, context)
      } catch (error) {
        console.warn(`转换块失败 (type: ${block.type}):`, error)
        return this.createFallbackHtml(block, context)
      }
    }

    // 如果没有找到对应的转换器，使用后备方案
    return this.createFallbackHtml(block, context)
  }

  /**
   * 创建后备HTML（用于不支持的块类型）
   */
  private createFallbackHtml(block: Blocks, context: ConversionContext): string {
    const className = `${context.options.cssClassPrefix || 'feishu'}-unsupported`

    // 尝试提取文本内容
    const textContent = this.extractTextContent(block)

    if (textContent) {
      if (context.options.useInlineStyles !== false) {
        return `<div style="padding: 8px; border: 1px dashed #ccc; background: #f9f9f9; color: #666;">[不支持的块类型: ${block.type}] ${textContent}</div>`
      } else {
        return `<div class="${className}">[不支持的块类型: ${block.type}] ${textContent}</div>`
      }
    }

    return `<!-- 不支持的块类型: ${block.type} -->`
  }

  /**
   * 提取块的文本内容
   */
  private extractTextContent(block: Blocks): string {
    if (block.zoneState?.allText) {
      return block.zoneState.allText.trim()
    }

    if (block.zoneState?.content?.ops) {
      return block.zoneState.content.ops
        .map(op => op.insert)
        .join('')
        .trim()
    }

    return ''
  }

  /**
   * 生成CSS样式
   */
  private generateStyles(context: ConversionContext): string | undefined {
    if (context.options.useInlineStyles !== false) {
      return undefined
    }

    const prefix = context.options.cssClassPrefix || 'feishu'

    return `
.${prefix}-text {
  line-height: 1.6;
}

.${prefix}-heading-1 { font-size: 2em; font-weight: bold; margin: 24px 0 16px 0; }
.${prefix}-heading-2 { font-size: 1.5em; font-weight: bold; margin: 20px 0 12px 0; }
.${prefix}-heading-3 { font-size: 1.3em; font-weight: bold; margin: 16px 0 12px 0; }
.${prefix}-heading-4 { font-size: 1.1em; font-weight: bold; margin: 16px 0 8px 0; }
.${prefix}-heading-5 { font-size: 1em; font-weight: bold; margin: 12px 0 8px 0; }
.${prefix}-heading-6 { font-size: 0.9em; font-weight: bold; margin: 12px 0 8px 0; }

.${prefix}-divider {
  border: none;
  border-top: 1px solid #ccc;
  margin: 16px 0;
}

.${prefix}-code {
  background: #f6f8fa;
  border: 1px solid #d1d9e0;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
  overflow: auto;
  font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 14px;
  line-height: 1.45;
}

.${prefix}-quote, .${prefix}-callout {
  /* 移除装饰样式，保持简洁 */
}

.${prefix}-callout {
  /* 移除装饰样式，保持简洁 */
}

.${prefix}-bullet-list, .${prefix}-ordered-list {
   padding-left: 24px;
   margin: 16px 0;
}

.${prefix}-bullet-list li::marker, .${prefix}-ordered-list li::marker {
  color: #0084ff;
}

.${prefix}-todo-list {
  list-style: none;
  padding-left: 0;
  margin: 16px 0;
 }

.${prefix}-bullet, .${prefix}-ordered {
  margin: 4px 0;
}

.${prefix}-todo {
  list-style: none;
  margin: 4px 0;
  display: flex;
  align-items: flex-start;
}

.${prefix}-todo input {
  margin-right: 8px;
  margin-top: 2px;
}

.${prefix}-todo .checked {
  text-decoration: line-through;
  color: #888;
}

.${prefix}-image {
  max-width: 100%;
  height: auto;
  margin: 16px 0; 
}

.${prefix}-table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
  border: 1px solid #ddd;
}

.${prefix}-cell {
  border: 1px solid #ddd;
  padding: 8px;
  vertical-align: top;
}

.${prefix}-file {
  display: inline-flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #d1d9e0;
  border-radius: 6px;
  background: #f6f8fa;
  text-decoration: none;
  color: #0969da;
  margin: 8px 0;
  transition: background-color 0.2s;
}

.${prefix}-file:hover {
  background: #f0f6ff;
}

.${prefix}-file .file-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.${prefix}-whiteboard {
  max-width: 100%;
  height: auto;
  margin: 16px 0;
  border: 2px dashed #d1d9e0;
  border-radius: 8px;
  background: #f6f8fa;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #656d76;
  font-size: 14px;
}

.${prefix}-whiteboard .whiteboard-placeholder {
  text-align: center;
}

.${prefix}-whiteboard .whiteboard-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.${prefix}-whiteboard .whiteboard-hint {
  font-size: 12px;
  color: #8c959f;
  margin-top: 4px;
}

.${prefix}-iframe {
  width: 100%;
  border: 1px solid #d1d9e0;
  border-radius: 6px;
  margin: 16px 0;
}

.${prefix}-iframe-placeholder {
  width: 100%;
  height: 200px;
  border: 2px dashed #d1d9e0;
  border-radius: 6px;
  margin: 16px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f6f8fa;
  color: #656d76;
}

.${prefix}-iframe-placeholder .placeholder-content {
  text-align: center;
}

.${prefix}-iframe-placeholder .placeholder-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.${prefix}-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin: 16px 0;
  align-items: flex-start;
}

.${prefix}-grid-column {
  flex: 1;
  min-width: 0;
  box-sizing: border-box;
}

.${prefix}-unsupported {
  padding: 8px;
  border: 1px dashed #ccc;
  background: #f9f9f9;
  color: #666;
  margin: 8px 0;
}
    `.trim()
  }

  /**
   * 转换顶级列表组
   */
  private convertTopLevelListGroup(listBlocks: Blocks[], listType: BlockType, context: ConversionContext): string {
    if (!listBlocks.length) return ''

    const listItems = listBlocks.map(block => this.convertBlock(block, context)).filter(Boolean)
    if (!listItems.length) return ''

    const listStyle = this.getTopLevelListStyle(listType, context)
    const listTag = this.getListTag(listType)

    // 为有序列表和无序列表添加标记颜色样式
    if (context.options.useInlineStyles !== false && (listType === BlockType.ORDERED || listType === BlockType.BULLET)) {
      const markerStyle = 'li::marker { color: #0084ff; }'
      return `<style>${markerStyle}</style><${listTag} style="${listStyle}">${listItems.join('')}</${listTag}>`
    }

    const listClassName = this.getClassName(
      listType === BlockType.TODO ? 'todo-list' :
        listType === BlockType.ORDERED ? 'ordered-list' : 'bullet-list',
      context
    )

    return `<${listTag} class="${listClassName}">${listItems.join('')}</${listTag}>`
  }

  /**
   * 分组连续的同类型列表块（用于顶级处理）
   */
  private groupConsecutiveListBlocks(children: Blocks[]): Array<{ type: 'list' | 'single', blocks: Blocks[], listType?: BlockType }> {
    const groups: Array<{ type: 'list' | 'single', blocks: Blocks[], listType?: BlockType }> = []
    let currentListGroup: Blocks[] = []
    let currentListType: BlockType | undefined = undefined

    const isListType = (blockType: BlockType): boolean =>
      blockType === BlockType.BULLET ||
      blockType === BlockType.ORDERED ||
      blockType === BlockType.TODO

    for (const child of children) {
      if (isListType(child.type)) {
        if (currentListType !== child.type) {
          // 新的列表类型，先保存之前的分组
          if (currentListGroup.length > 0) {
            groups.push({ type: 'list', blocks: currentListGroup, listType: currentListType })
          }
          currentListGroup = [child]
          currentListType = child.type
        } else {
          // 相同的列表类型，添加到当前分组
          currentListGroup.push(child)
        }
      } else {
        // 非列表类型，先保存之前的列表分组
        if (currentListGroup.length > 0) {
          groups.push({ type: 'list', blocks: currentListGroup, listType: currentListType })
          currentListGroup = []
          currentListType = undefined
        }
        // 单个非列表块
        groups.push({ type: 'single', blocks: [child] })
      }
    }

    // 处理最后的列表分组
    if (currentListGroup.length > 0) {
      groups.push({ type: 'list', blocks: currentListGroup, listType: currentListType })
    }

    return groups
  }

  /**
   * 获取列表标签
   */
  private getListTag(listType: BlockType): string {
    return listType === BlockType.ORDERED ? 'ol' : 'ul'
  }

  /**
   * 获取顶级列表样式
   */
  private getTopLevelListStyle(listType: BlockType, context: ConversionContext): string {
    if (context.options.useInlineStyles === false) {
      return ''
    }

    switch (listType) {
      case BlockType.TODO:
        return 'list-style: none; padding-left: 0; margin: 16px 0;'
      case BlockType.ORDERED:
      case BlockType.BULLET:
        return 'padding-left: 24px; margin: 16px 0;'
      default:
        return 'padding-left: 24px; margin: 16px 0;'
    }
  }

  /**
   * 获取CSS类名
   */
  private getClassName(type: string, context: ConversionContext): string {
    const prefix = context.options.cssClassPrefix || 'feishu'
    return `${prefix}-${type}`
  }
} 