import { Minute } from "../common"
import { showConfirm } from "@/entrypoints/utils/message-helper"

export const confirm = (): Promise<boolean> => {
  return showConfirm({
    message: 'continue',
    confirmText: 'confirm',
    duration: Minute
  })
}

export const confirmWithCancel = (message: string = '是否继续导出？'): Promise<boolean> => {
  return showConfirm({
    message,
    confirmText: '继续导出',
    cancelText: '取消',
    duration: Minute
  })
}

// 显示带取消按钮的进度提示，返回一个可以更新内容和检查是否取消的对象
export const showCancelableProgress = (initialMessage: string, key: string = 'cancelableProgress') => {
  let isCancelled = false;

  // 显示加载消息
  window.postMessage({
    type: 'FEISHU_EXPORT_MESSAGE',
    message: initialMessage,
    messageType: 'loading',
    duration: 0,
    keepAlive: true,
    key: key,
    actionText: '取消导出',
    showConfirm: true,
    confirmButtonText: '取消导出',
    cancelButtonText: '继续'
  }, '*')

  // 监听取消操作
  const handleCancel = (event: MessageEvent) => {
    if (event.data && event.data.type === 'MESSAGE_ACTION' && event.data.action === 'confirm') {
      isCancelled = true;
      window.removeEventListener('message', handleCancel);
      // 移除消息
      window.postMessage({
        type: 'FEISHU_EXPORT_REMOVE_MESSAGE',
        key: key
      }, '*')
    }
  }

  window.addEventListener('message', handleCancel);

  return {
    updateMessage: (message: string) => {
      if (!isCancelled) {
        window.postMessage({
          type: 'FEISHU_EXPORT_MESSAGE',
          message: message,
          messageType: 'loading',
          duration: 0,
          keepAlive: true,
          key: key,
          actionText: '取消导出',
          showConfirm: true,
          confirmButtonText: '取消导出',
          cancelButtonText: '继续'
        }, '*')
      }
    },
    isCancelled: () => isCancelled,
    close: () => {
      window.removeEventListener('message', handleCancel);
      window.postMessage({
        type: 'FEISHU_EXPORT_REMOVE_MESSAGE',
        key: key
      }, '*')
    }
  };
};
