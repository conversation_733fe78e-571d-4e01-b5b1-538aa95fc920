import { Minute } from "../common"

export const confirm = (): Promise<boolean> => {
  return Promise.resolve(window.confirm('continue'))
}

export const confirmWithCancel = (message: string = '是否继续导出？'): Promise<boolean> => {
  return Promise.resolve(window.confirm(message))
}

// 显示带取消按钮的进度提示，返回一个可以更新内容和检查是否取消的对象
export const showCancelableProgress = (initialMessage: string, key: string = 'cancelableProgress') => {
  let isCancelled = false;

  // 由于不再使用message组件，这里简化为只显示loading消息，不提供取消功能
  // 如果需要取消功能，建议使用原生的confirm对话框
  console.log(`[${key}] ${initialMessage}`);

  return {
    updateMessage: (message: string) => {
      if (!isCancelled) {
        console.log(`[${key}] ${message}`);
      }
    },
    isCancelled: () => isCancelled,
    close: () => {
      console.log(`[${key}] 完成`);
    }
  };
};
