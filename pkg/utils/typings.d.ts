export type AddOnPrefix<S extends string> = `on${Capitalize<S>}`
export type Emits2Events<T> = {
  [K in keyof T as AddOnPrefix<string & K>]?: T[K]
}
export type JsonSimpleValue = string | number | boolean | null | undefined
export type JsonObject = { [key: string]: JsonValue }
export type JsonArray = JsonValue[]
export type JsonValue = JsonSimpleValue | JsonObject | JsonArray

export type ClassName = string | string[] | object | null | false | undefined | 0
export type Prettify<T> = {
  [K in keyof T]: T[K];
} & {}
type DeepPrettify<T> = {
  [K in keyof T]: T[K] extends object ? DeepPrettify<T[K]> : T[K];
}

type Merge<T> = {
  [K in keyof T]: T[K];
}
type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never
type MagicType<U> =
  UnionToIntersection<U> extends infer O ? { [K in keyof O]: O[K] } : never
type ApplyMagicType<T extends StringObject> = {
  [K in keyof T]: T[K] extends string
  ? MagicType<T[K]>
  : T[K] extends StringObject
  ? ApplyMagicType<T[K]>
  : never;
}
type ValueOf<T> = DeepPrettify<MagicType<T[keyof T]>>

// 定义一个类型来判断两个类型是否相等
type IsEqual<T, U> =
  (<G>() => G extends T ? 1 : 2) extends
  (<G>() => G extends U ? 1 : 2)
  ? true
  : false

export type RecursiveKeyCheck<T, U> = T extends object ? U extends object
  ? ({
    [K in keyof T]: K extends keyof U
    ? RecursiveKeyCheck<T[K], U[K]>
    : false;
  } & {
    [K in keyof U]: K extends keyof T
    ? RecursiveKeyCheck<T[K], U[K]>
    : false;
  })
  : false
  : IsEqual<T, U>

type IsNever<T> = [T] extends [never] ? true : false
export type AllTrue<T extends object> =
  IsNever<T[keyof T]> extends true
  ? true
  : T[keyof T] extends true
  ? true
  : T[keyof T] extends false
  ? false
  : T[keyof T] extends object ? AllTrue<T[keyof T]> : (2 | T[keyof T])

export type StringObject = {
  [key: string]: string | StringObject
}

export type ContainsTrue<T> = true extends T ? true : false
export type ContainsFalse<T> = false extends T ? true : false
