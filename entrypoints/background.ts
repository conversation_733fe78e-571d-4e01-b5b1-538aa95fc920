import { openDB } from 'idb'
import { getTodosRepo, registerTodosRepo } from './utils/service';
import { authStorage } from './utils/auth-client';
// 添加操作统计客户端导入
import { operationStatsClient, type OperationType } from './utils/download-stats';
import { recordOperationStats } from './api/api';
import { executeScriptByFlag } from './utils/inject-helper';
// 直接导入 Puppeteer 模块
import { connect as puppeteerConnect, ExtensionTransport } from 'puppeteer-core/lib/esm/puppeteer/puppeteer-core-browser.js';

export default defineBackground( () => {
  console.log('Hello background!', { id: browser.runtime.id });

  // 创建隐藏窗口来渲染HTML内容
  async function createHtmlWindow(htmlContent: string) {
    try {
      console.log('🏗️ Creating hidden window for HTML content...');

      // 创建一个 data URL 包含HTML内容
      const encodedHtml = encodeURIComponent(htmlContent);
      const dataUrl = `data:text/html;charset=utf-8,${encodedHtml}`;

      // 创建一个隐藏的窗口，位置在屏幕外
      const hiddenWindow = await browser.windows.create({
        url: dataUrl,
        type: 'popup',       // 使用 popup 类型，更不显眼
        focused: false,      // 不获取焦点
        width: 1,            // 最小宽度
        height: 1,           // 最小高度
        left: -3000,         // 位置在屏幕外
        top: -3000           // 位置在屏幕外
      });

      console.log(`🪟 Created hidden window ${hiddenWindow.id} for HTML content`);

      if (!hiddenWindow.tabs || !hiddenWindow.tabs[0].id) {
        throw new Error('Failed to create hidden window - no tabs found');
      }

      const tab = hiddenWindow.tabs[0];
      console.log(`🎯 Hidden window tab ${tab.id} created`);

      // 创建窗口后立即最小化（如果支持）
      try {
        await browser.windows.update(hiddenWindow.id!, {
          state: 'minimized',
          focused: false
        });
        console.log('🔽 Window minimized successfully');
      } catch (minimizeError) {
        console.warn('⚠️ Could not minimize window, but proceeding:', minimizeError);
      }

      // 等待页面加载完成
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          browser.tabs.onUpdated.removeListener(listener);
          reject(new Error('Hidden window loading timeout after 15 seconds'));
        }, 15000);

        function listener(tabId: number, changeInfo: any, tab: any) {
          if (tabId === tab.id) {
            console.log(`Tab ${tabId} update:`, changeInfo, 'URL:', tab.url);

            // 确保页面加载完成
            if (changeInfo.status === 'complete' &&
              tab.url &&
              tab.url !== 'about:blank') {
              clearTimeout(timeout);
              browser.tabs.onUpdated.removeListener(listener);
              console.log(`✅ Hidden window tab ${tabId} loaded successfully`);
              resolve();
            }
          }
        }
        browser.tabs.onUpdated.addListener(listener);

        // 额外的安全检查：如果标签页已经加载完成
        browser.tabs.get(tab.id!).then(currentTab => {
          if (currentTab.status === 'complete' &&
            currentTab.url &&
            currentTab.url !== 'about:blank') {
            clearTimeout(timeout);
            browser.tabs.onUpdated.removeListener(listener);
            console.log(`✅ Hidden window tab ${tab.id} was already loaded`);
            resolve();
          }
        }).catch(err => {
          console.warn('Error checking tab status:', err);
        });
      });

      // 稍等一下确保页面完全渲染
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log(`🔗 Attempting to connect to hidden window tab ${tab.id}`);

      if (!tab.id) {
        throw new Error('Tab ID is undefined');
      }

      // 使用 ExtensionTransport 连接到标签页
      const browserInstance = await puppeteerConnect({
        transport: await ExtensionTransport.connectTab(tab.id),
      });

      console.log(`✅ Successfully connected to hidden window tab ${tab.id}`);

      // 获取页面对象
      const [page] = await browserInstance.pages();

      // 验证页面 URL
      const pageUrl = await page.url();
      console.log(`📄 Page URL: ${pageUrl}`);

      return { browser: browserInstance, page, tabId: tab.id, windowId: hiddenWindow.id };
    } catch (error) {
      console.error('❌ 创建HTML隐藏窗口失败:', error);
      throw error;
    }
  }

  // 处理 PDF 生成请求
  async function handlePdfGeneration(htmlContent: string, title?: string, options = {}) {
    let browserInstance: any = null;
    let tabId: number | undefined;
    let windowId: number | undefined;

    try {
      if (!htmlContent) {
        throw new Error('HTML content is required');
      }

      console.log('=== PDF Generation Started ===');
      console.log('HTML content length:', htmlContent.length);
      console.log('Options received:', options);

      // 创建隐藏窗口并连接到 Puppeteer
      const connectionResult = await createHtmlWindow(htmlContent);

      browserInstance = connectionResult.browser;
      const page = connectionResult.page;
      tabId = connectionResult.tabId;
      windowId = connectionResult.windowId;

      console.log('🎯 Generating PDF from HTML content...');

      // 等待页面完全加载
      try {
        await page.waitForSelector('body', { timeout: 15000 });

        // 确保文档完全加载
        await page.waitForFunction('() => document.readyState === "complete"', { timeout: 10000 });

        // 额外等待，确保内容渲染完成
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (waitError) {
        console.warn('Page load timeout, proceeding with PDF generation:', waitError);
      }

      // 默认 PDF 配置
      const defaultPDFOptions = {
        format: 'A4' as const,
        printBackground: true,
        margin: {
          top: '20px',
          right: '20px',
          bottom: '20px',
          left: '20px'
        },
        displayHeaderFooter: true,
        footerTemplate: "<div style='font-size: 10px; text-align: center; width: 100%;'>第 <span class='pageNumber'></span> 页 / 共 <span class='totalPages'></span> 页</div>",
        ...options
      };

      console.log('PDF options:', defaultPDFOptions);

      // 生成 PDF
      const pdfBuffer = await page.pdf(defaultPDFOptions);

      console.log(`✅ PDF generated, size: ${pdfBuffer.length} bytes`);

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = title ? `${title}.pdf` : `document-${timestamp}.pdf`;

      // 将 PDF buffer 转换为 base64
      const uint8Array = new Uint8Array(pdfBuffer);
      let binary = '';
      for (let i = 0; i < uint8Array.length; i++) {
        binary += String.fromCharCode(uint8Array[i]);
      }
      const base64Data = btoa(binary);

      // 在service worker中直接使用data URL，因为URL.createObjectURL不可用
      const dataUrl = `data:application/pdf;base64,${base64Data}`;

      console.log('📥 Triggering download from background script...');

      // 在后台脚本中触发下载
      const downloadId = await browser.downloads.download({
        url: dataUrl,
        filename: filename,
        saveAs: true  // 这会触发"另存为"对话框
      });

      console.log(`🚀 Background download started with ID: ${downloadId}, filename: ${filename}`);

      // 监听下载完成事件
      const onDownloadChanged = (delta: any) => {
        if (delta.id === downloadId && delta.state && delta.state.current === 'complete') {
          browser.downloads.onChanged.removeListener(onDownloadChanged);
          console.log(`✅ Background download completed: ${filename}`);
        }
      };
      browser.downloads.onChanged.addListener(onDownloadChanged);

      return {
        success: true,
        message: `PDF 已生成并开始下载: ${filename}`,
        downloadId: downloadId,
        filename: filename
      };
    } catch (error) {
      console.error('PDF 生成失败:', error);
      throw error;
    } finally {
      // 清理资源
      try {
        if (browserInstance) {
          console.log('🔌 Disconnecting browser instance...');
          await browserInstance.disconnect();
        }

        if (windowId) {
          console.log(`🗑️ Closing hidden window ${windowId}...`);
          await browser.windows.remove(windowId);
        } else if (tabId) {
          console.log(`🗑️ Closing tab ${tabId}...`);
          await browser.tabs.remove(tabId);
        }
      } catch (cleanupError) {
        console.warn('⚠️ 清理资源时出错:', cleanupError);
      }
    }
  }

  const db = openDB('todos', 1, {
    upgrade(db) {
      if (!db.objectStoreNames.contains('todos')) {
        db.createObjectStore('todos', {
          keyPath: 'id',
        })
      }
    },
  })
  registerTodosRepo(db)

  browser.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
    // 处理认证相关的消息
    if (message.action === 'getAuthToken') {
      try {
        const token = await authStorage.getToken()
        sendResponse({ success: true, token: token || false })
      } catch (error) {
        console.error('获取认证token失败:', error)
        sendResponse({ success: false, error: '获取认证token失败' })
      }
      return true
    } else if (message.action === 'setAuthToken') {
      try {
        await authStorage.setToken(message.token)
        sendResponse({ success: true })
      } catch (error) {
        console.error('保存认证token失败:', error)
        sendResponse({ success: false, error: '保存认证token失败' })
      }
      return true
    } else if (message.action === 'clearAuthToken') {
      try {
        await authStorage.clearToken()
        sendResponse({ success: true })
      } catch (error) {
        console.error('清除认证token失败:', error)
        sendResponse({ success: false, error: '清除认证token失败' })
      }
      return true
    } else if (message.action === 'checkAuthStatus') {
      try {
        const isLoggedIn = await authStorage.isLoggedIn()
        sendResponse({ success: true, isLoggedIn })
      } catch (error) {
        console.error('检查登录状态失败:', error)
        sendResponse({ success: false, error: '检查登录状态失败' })
      }
      return true
    } else if (message.action === 'exportPdfAll') {
      // 如果需要记录统计，先记录
      if (message.needStats) {
        recordOperationStats(message.action).catch(console.error);
      }

      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })

        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await executeScriptByFlag(activeTabId, 'exportPdfAll', {})
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'exportPdf') {
      // 如果需要记录统计，先记录
      if (message.needStats) {
        await recordOperationStats(message.action).catch(console.error);
      }

      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })

        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await executeScriptByFlag(activeTabId, 'exportPdf', {})
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'exportImg') {
      // 如果需要记录统计，先记录
      if (message.needStats) {
        await recordOperationStats(message.action).catch(console.error);
      }

      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })

        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await executeScriptByFlag(activeTabId, 'exportImg', {})
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'exportWord') {
      // 如果需要记录统计，先记录
      if (message.needStats) {
        await recordOperationStats(message.action).catch(console.error);
      }

      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })
        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await executeScriptByFlag(activeTabId, 'word', {})
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'exportMd') {
      // 如果需要记录统计，先记录
      if (message.needStats) {
        await recordOperationStats(message.action).catch(console.error);
      }

      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })
        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await executeScriptByFlag(activeTabId, 'md', {})
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'copyToFeishu') {
      // 如果需要记录统计，先记录
      if (message.needStats) {
        await recordOperationStats(message.action).catch(console.error);
      }

      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })
        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await executeScriptByFlag(activeTabId, 'copyToFeishu', {})
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'preview') {
      const res = await fetch('http://localhost:3001/pdf', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          "html": message.data,
          "options": {
            "format": "A4",
            "printBackground": true,
            "margin": {
              "top": "30px",
              "right": "30px",
              "bottom": "30px",
              "left": "30px"
            },
            "displayHeaderFooter": true,
            "footerTemplate": "<div style='font-size: 10px; text-align: center; width: 100%;'>第 <span class='pageNumber'></span> 页 / 共 <span class='totalPages'></span> 页</div>",
            "preferCSSPageSize": true
          }
        }),
      })

      const blob = await res.blob()

      // 将 blob 转换为 base64 data URL
      const arrayBuffer = await blob.arrayBuffer()
      const uint8Array = new Uint8Array(arrayBuffer)
      let binaryString = ''
      for (let i = 0; i < uint8Array.length; i++) {
        binaryString += String.fromCharCode(uint8Array[i])
      }
      const base64 = btoa(binaryString)
      const dataUrl = `data:application/pdf;base64,${base64}`

      // 使用 chrome.downloads API 下载文件
      await browser.downloads.download({
        url: dataUrl,
        filename: `${message.title || '文档'}.pdf`,
        saveAs: true // 显示保存对话框
      })
      return true;
    } else if (message.action === 'copyToFeishuFromContent') {

      const executeParseHtmlTest = async () => {

        try {
          // 获取当前时间，格式化为 YYYY-MM-DD HH:mm:ss
          const now = new Date()
          const formatTime = (date: Date) => {
            const year = date.getFullYear()
            const month = String(date.getMonth() + 1).padStart(2, '0')
            const day = String(date.getDate()).padStart(2, '0')
            const hours = String(date.getHours()).padStart(2, '0')
            const minutes = String(date.getMinutes()).padStart(2, '0')
            const seconds = String(date.getSeconds()).padStart(2, '0')
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
          }

          // 动态生成广告HTML，使用真实的数据
          const currentTime = formatTime(now)
          const documentTitle = message.title || '未知文档'
          const originalUrl = message.currentUrl || '#'

          const adHtml = `<blockquote><span>🔗 原文链接：</span><a class="_origin_" href="${originalUrl}">${documentTitle}</a></blockquote><blockquote><span>⏰ 剪存时间：${currentTime}</span></blockquote><blockquote><span>✂️ 本文档由 <a href="https://chromewebstore.google.com/detail/%E9%A3%9E%E4%B9%A6%E5%8A%A9%E6%89%8B-%E9%A3%9E%E4%B9%A6lark%E5%AF%BC%E5%87%BA/cfenjfhlhjpkaaobmhbobajnnhifilbl">飞书助手</a> 生成</span></blockquote><hr/>`

          // 从消息中获取真实的HTML内容
          const htmlContent = adHtml + message.htmlContent || ''


          if (!htmlContent) {
            console.error('❌ HTML内容为空，无法继续')
            return
          }

          // 设置测试参数
          const title = message.title || '测试文档_' + new Date().getTime()



          // 准备FormData
          const formData = new FormData()
          formData.append("description", "")
          formData.append("cover", '//lf-package-cn.feishucdn.com/obj/feishu-static/ccm-vmok/web/feishu.ico')
          formData.append("docType", "docx")
          formData.append("originUrl", message.currentUrl)
          formData.append("clipVersion", "1.0.39")
          formData.append("lang", "zh-CN")

          // 创建HTML文件blob
          const htmlBlob = new Blob([htmlContent], { type: "text/html" })
          formData.append("file", htmlBlob, encodeURIComponent(title))



          // 调用parse_html接口
          const apiUrl = 'https://internal-api-space.feishu.cn/space/api/parser/wiki/parse_html/'

          const response = await fetch(apiUrl, {
            method: 'POST',
            credentials: 'include', // 包含cookies用于身份验证
            headers: {
              'x-request-source': 'feishu-clipper'
            },
            body: formData
          })


          if (response.ok) {
            const responseData = await response.json()


            // 发送上传成功消息到content script
            if (sender.tab?.id) {
              browser.tabs.sendMessage(sender.tab.id, {
                type: 'SHOW_MESSAGE',
                message: '正在等待飞书创建文档...',
                messageType: 'info',
                duration: 0 // 不自动隐藏，等待后续状态更新
              })
            }

            // 如果有ticket，开始轮询处理状态
            if (responseData.ticket) {

              // 实现轮询逻辑
              const pollResult = async (ticketId: string) => {
                let retryCount = 50  // 最大重试50次

                for (; ;) {
                  try {
                    // 构造轮询URL（添加防缓存参数）
                    const randomStr = Math.random().toString(36).substring(2, 15)
                    const timestamp = Date.now()
                    const pollUrl = `https://internal-api-space.feishu.cn/space/api/parser/wiki/clip/result?_r=${randomStr}_${timestamp}&ticket=${ticketId}`


                    const pollResponse = await fetch(pollUrl, {
                      method: 'GET',
                      credentials: 'include',
                      headers: {
                        'x-request-source': 'feishu-clipper'
                      }
                    })

                    if (!pollResponse.ok) {
                      throw new Error(`HTTP error! status: ${pollResponse.status}`)
                    }

                    const responseText = await pollResponse.text()

                    // 检查是否返回空字符串（表示还在处理中）
                    if (responseText !== "") {
                      try {
                        const result = JSON.parse(responseText)

                        // 检查result是否为null，如果是null说明还在处理中，继续轮询
                        if (result === null) {
                          // 不return，继续下一次轮询
                        } else {
                          // 只有当result不是null时才认为处理完成
                          if (result?.docUrl) {

                            // 发送成功消息到content script
                            if (sender.tab?.id) {
                              browser.tabs.sendMessage(sender.tab.id, {
                                type: 'SHOW_MESSAGE',
                                message: '复制到飞书成功！请在新打开的窗口中查看',
                                messageType: 'success',
                                duration: 5000 // 成功后5秒自动隐藏
                              })
                            }

                            // 打开新tab，跳转到文档URL，在当前tab右侧创建
                            const currentTab = sender.tab
                            await browser.tabs.create({
                              url: result.docUrl,
                              active: false,
                              index: currentTab ? currentTab.index + 1 : undefined,
                            })
                          }
                          return result
                        }
                      } catch (parseError) {
                        console.error('❌ 解析JSON失败:', parseError)
                        console.error('原始响应:', responseText)
                        // 解析失败也继续轮询，可能是临时问题
                        console.error('🔄 解析失败，继续轮询...')
                      }
                    }

                    // 重试次数用完了
                    if (--retryCount <= 0) {
                      console.error('⏰ 50次轮询后仍未获取到结果，超时退出')

                      // 发送超时消息到content script
                      if (sender.tab?.id) {
                        browser.tabs.sendMessage(sender.tab.id, {
                          type: 'SHOW_MESSAGE',
                          message: '复制到飞书超时，请重试',
                          messageType: 'warning',
                          duration: 5000
                        })
                      }
                      break
                    }

                    // 等待3秒后重试
                    console.error(`⏳ 等待3秒后进行第${51 - retryCount}次重试...`)
                    await new Promise(resolve => setTimeout(resolve, 1000))

                  } catch (error) {
                    console.error('💥 轮询过程中出错:', error)
                    throw error
                  }
                }

                return null  // 超时或失败
              }

              // 开始轮询
              pollResult(responseData.ticket).catch(console.error)
            }
          } else {
            const errorText = await response.text()
            console.error('❌ 接口调用失败:', errorText)

            // 发送失败消息到content script
            if (sender.tab?.id) {
              browser.tabs.sendMessage(sender.tab.id, {
                type: 'SHOW_MESSAGE',
                message: '复制到飞书失败，请重试',
                messageType: 'error',
                duration: 5000
              })
            }
          }

        } catch (error) {
          console.error('💥 调用过程中出错:', error)

          // 发送错误消息到content script
          if (sender.tab?.id) {
            browser.tabs.sendMessage(sender.tab.id, {
              type: 'SHOW_MESSAGE',
              message: '复制到飞书过程中出错，请重试',
              messageType: 'error',
              duration: 5000
            })
          }
        }
      }

      executeParseHtmlTest().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'exportHtml') {
      // 如果需要记录统计，先记录
      if (message.needStats) {
        await recordOperationStats(message.action).catch(console.error);
      }

      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })
        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await executeScriptByFlag(activeTabId, 'exportHtml', {})
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'exportCollect') {
      // 如果需要记录统计，先记录
      if (message.needStats) {
        await recordOperationStats(message.action).catch(console.error);
      }

      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })
        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await executeScriptByFlag(activeTabId, 'exportCollect', {})
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'collectData') {
      browser.storage.local.set({
        currentPreview: message.action
      })
      const todosRepo = getTodosRepo()
      await todosRepo.update({
        id: message.action,
        data: message.data.pageContent,
        title: message.data.pageTitle,
        originalUrl: message.data.originalUrl
      })



      // 查找是否已经存在预览页面
      const previewUrl = browser.runtime.getURL("/preview-new.html")
      const existingTabs = await browser.tabs.query({ url: previewUrl })

      if (existingTabs.length > 0) {
        // 如果存在预览页面，激活第一个并刷新
        const existingTab = existingTabs[0]
        await browser.tabs.update(existingTab.id!, {
          active: true
        })
        await browser.tabs.reload(existingTab.id!)
      } else {

        // 获取当前活动tab信息，在其右侧创建新tab
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })
        const currentTab = activeTabs[0]

        await browser.tabs.create({
          url: previewUrl,
          active: true,
          index: currentTab ? currentTab.index + 1 : undefined,
        })
      }
    } else if (message.action === 'breakCopy') {
      // 如果需要记录统计，先记录
      if (message.needStats) {
        await recordOperationStats(message.action).catch(console.error);
      }

      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })
        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await browser.tabs.sendMessage(activeTabId, {
            action: 'breakCopy',
          })
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === 'login') {
      // 查找是否已经存在预览页面
      const previewUrl = browser.runtime.getURL("/login.html")
      const existingTabs = await browser.tabs.query({ url: previewUrl })

      if (existingTabs.length > 0) {
        // 如果存在预览页面，激活第一个并刷新
        const existingTab = existingTabs[0]
        await browser.tabs.update(existingTab.id!, {
          active: true
        })
        await browser.tabs.reload(existingTab.id!)
      } else {

        // 获取当前活动tab信息，在其右侧创建新tab
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })
        const currentTab = activeTabs[0]

        await browser.tabs.create({
          url: previewUrl,
          active: true,
          index: currentTab ? currentTab.index + 1 : undefined,
        })
      }

      return true
    } else if (message.action === "breakCopy2") {
      browser.storage.local.set({
        currentPreview: message.action
      })
      const todosRepo = getTodosRepo()
      await todosRepo.update({
        id: message.action,
        data: message.data,
        title: message.title,
        originalUrl: message.originalUrl
      })



      // 查找是否已经存在预览页面
      const previewUrl = browser.runtime.getURL("/preview.html")
      const existingTabs = await browser.tabs.query({ url: previewUrl })

      if (existingTabs.length > 0) {
        // 如果存在预览页面，激活第一个并刷新
        const existingTab = existingTabs[0]
        await browser.tabs.update(existingTab.id!, {
          active: true
        })
        await browser.tabs.reload(existingTab.id!)
      } else {

        // 获取当前活动tab信息，在其右侧创建新tab
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })
        const currentTab = activeTabs[0]

        await browser.tabs.create({
          url: previewUrl,
          active: true,
          index: currentTab ? currentTab.index + 1 : undefined,
        })
      }

      return true
    } else if (message.action === "preview2") {
      // 如果需要记录统计，先记录
      if (message.needStats) {
        await recordOperationStats(message.action).catch(console.error);
      }

      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })

        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await executeScriptByFlag(activeTabId, 'preview2', {})
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === "handlePreview") {
      // 如果需要记录统计，先记录
      if (message.needStats) {
        await recordOperationStats(message.action).catch(console.error);
      }

      const executeScript = async () => {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        })

        const activeTabId = activeTabs.at(0)?.id

        if (activeTabs.length === 1 && activeTabId !== undefined) {
          await executeScriptByFlag(activeTabId, 'preview2', {})
        }
      }
      executeScript().then(sendResponse).catch(console.error)
      return true
    } else if (message.action === "exportPdfNew") {
      console.log('111', 111)
      // 如果需要记录统计，先记录
      if (message.needStats) {
        await recordOperationStats(message.action).catch(console.error);
      }
      console.log('2222', 2222)
      try {
        console.log('🚀 开始 exportPdfNew 处理...');
        console.log('3333', 3333)
        // 从消息中获取HTML内容和标题
        const htmlContent = message.htmlContent;
        const title = message.title || '飞书文档';
        const options = message.options || {};

        if (!htmlContent) {
          throw new Error('HTML内容为空，无法生成PDF');
        }

        console.log(`📄 正在为文档 "${title}" 生成PDF...`);

        // 调用PDF生成函数
        const result = await handlePdfGeneration(htmlContent, title, options);

        console.log('✅ PDF生成成功:', result);
        sendResponse({ success: true, result });

      } catch (error) {
        console.error('❌ exportPdfNew 处理失败:', error);
        sendResponse({
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
      }

      return true;
    }
  })
});
