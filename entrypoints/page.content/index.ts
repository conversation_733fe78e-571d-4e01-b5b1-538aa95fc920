   import "~/assets/tailwind.css";
  import { createApp } from 'vue';
 import App from './App.vue';
 import 'highlight.js/styles/atom-one-light.min.css'
 import 'highlight.js/lib/common';
 import hljsVuePlugin from "@highlightjs/vue-plugin";
 
 export default defineContentScript({
   matches: [
     "https://*.feishu.cn/*",
     "https://*.feishu.net/*",
     "https://*.larksuite.com/*",
     "https://*.feishu-pre.net/*",
     "https://*.larkoffice.com/*",
   ],
   runAt: "document_end",
   // 2. Set cssInjectionMode
   cssInjectionMode: 'ui',
 
   async main(ctx) {
    // 在页面加载时立即添加全局事件拦截
    const interceptCopyEvents = () => {
      // 拦截复制事件
      const originalAddEventListener = EventTarget.prototype.addEventListener
      EventTarget.prototype.addEventListener = function(type, listener, options) {
        if (type === 'copy' || type === 'keydown' || type === 'keyup') {
          // 对于复制相关事件，添加我们的拦截器
          const wrappedListener = (event: Event) => {
            // 检查是否来自我们的shadow-root
            const target = event.target as Element
            if (target && target.closest && target.closest('feishu-content-ui')) {
              event.stopPropagation()
              event.stopImmediatePropagation()
              return
            }
            // 对于键盘快捷键，检查是否是Ctrl+C或Cmd+C
            if (type === 'keydown' || type === 'keyup') {
              const keyEvent = event as KeyboardEvent
              if ((keyEvent.ctrlKey || keyEvent.metaKey) && keyEvent.key === 'c') {
                event.stopPropagation()
                event.stopImmediatePropagation()
                return
              }
            }
            // 调用原始监听器
            if (listener && typeof listener === 'function') {
              listener.call(this, event)
            }
          }
          return originalAddEventListener.call(this, type, wrappedListener, options)
        }
        return originalAddEventListener.call(this, type, listener, options)
      }
    }

    // 立即执行拦截
    interceptCopyEvents()

    // 添加最高优先级的事件拦截
    document.addEventListener('copy', (event) => {
      // 检查是否来自我们的组件
      const target = event.target as Element
      if (target && target.closest && target.closest('feishu-content-ui')) {
        event.stopPropagation()
        event.stopImmediatePropagation()
      }
    }, true) // 使用capture阶段，确保最先执行

    document.addEventListener('keydown', (event) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'c') {
        const target = event.target as Element
        if (target && target.closest && target.closest('feishu-content-ui')) {
          event.stopPropagation()
          event.stopImmediatePropagation()
        }
      }
    }, true)

    // 3. Define your UI
     const ui = await createShadowRootUi(ctx, {
       name: 'feishu-content-ui',
       position: 'inline',
       anchor: '.root-block',
       mode:'open',
       onMount: (container) => {
 
         // Define how your UI will be mounted inside the container
         const app = createApp(App);
         app.use(hljsVuePlugin)

         app.mount(container);
         return app;
       },
       onRemove: (app) => {
         // Unmount the app when the UI is removed
         app?.unmount();
       },
     });
     browser.runtime.onMessage.addListener((message) => {
      if (message.action === 'breakCopy') {
        console.log('breakCopy')
        const children = document.body.querySelectorAll('.page-block-children')
        children.forEach(child => {
          child.remove()
        })
        console.log('children', children)
        ui.mount();
      }
    })

 
     
   },
 });