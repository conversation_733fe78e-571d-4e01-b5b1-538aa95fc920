<script lang="ts" setup>
import { onMounted } from "vue";

const codeContent = `export * from './env'
export * from './docx'
export * from './image'

export type * as mdast from 'mdast'
`;

// 处理复制事件，允许复制
const handleCopy = (event: ClipboardEvent) => {
  // 阻止事件冒泡和默认行为
  event.stopPropagation();
  event.stopImmediatePropagation();

  // 如果用户选择了文本，允许复制
  const selection = window.getSelection();
  if (selection && selection.toString()) {
    event.clipboardData?.setData("text/plain", selection.toString());
    event.preventDefault();
  }
};

// 处理鼠标事件，确保可以选择文本
const handleMouseDown = (event: MouseEvent) => {
  event.stopPropagation();
};

const handleMouseUp = (event: MouseEvent) => {
  event.stopPropagation();
};

onMounted(() => {
  // 添加全局复制事件监听，使用capture阶段确保优先处理
  document.addEventListener("copy", handleCopy, true);
  document.addEventListener("mousedown", handleMouseDown, true);
  document.addEventListener("mouseup", handleMouseUp, true);

  // 拦截键盘快捷键事件
  document.addEventListener(
    "keydown",
    (event) => {
      // 检测Ctrl+C (Windows) 或 Cmd+C (Mac)
      if ((event.ctrlKey || event.metaKey) && event.key === "c") {
        event.stopPropagation();
        event.stopImmediatePropagation();

        // 手动触发复制
        const selection = window.getSelection();
        if (selection && selection.toString()) {
          navigator.clipboard.writeText(selection.toString()).catch(() => {
            // 如果clipboard API失败，使用传统方法
            const textArea = document.createElement("textarea");
            textArea.value = selection.toString();
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand("copy");
            document.body.removeChild(textArea);
          });
        }
      }
    },
    true
  );

  // 拦截键盘快捷键事件
  document.addEventListener(
    "keyup",
    (event) => {
      // 检测Ctrl+C (Windows) 或 Cmd+C (Mac)
      if ((event.ctrlKey || event.metaKey) && event.key === "c") {
        event.stopPropagation();
        event.stopImmediatePropagation();
      }
    },
    true
  );

  // 添加右键菜单事件拦截
  document.addEventListener(
    "contextmenu",
    (event) => {
      const target = event.target as Element;
      if (target && target.closest && target.closest("feishu-content-ui")) {
        event.stopPropagation();
        event.stopImmediatePropagation();
      }
    },
    true
  );
});
</script>

<template>
  <highlightjs autodetect :code="codeContent" />
  <div
    class="injected-content"
    @copy="handleCopy"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseUp"
    style="
      user-select: text;
      -webkit-user-select: text;
      -moz-user-select: text;
      -ms-user-select: text;
    "
  >
    注入注入注入注入注入注入注入注入注入
  </div>
</template>

<style scoped>
.injected-content {
  /* 确保内容可以选择和复制 */
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;

  /* 防止继承父元素的复制限制 */
  -webkit-touch-callout: default !important;
  -webkit-tap-highlight-color: transparent;
}

/* 确保所有子元素都可以选择 */
.injected-content * {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}
</style>
