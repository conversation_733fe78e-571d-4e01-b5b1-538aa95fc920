// 消息类型定义
export type MessageType = 'success' | 'error' | 'warning' | 'info' | 'loading'

export interface MessageOptions {
  message: string
  type?: MessageType
  duration?: number // 毫秒，0表示不自动隐藏
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
  key?: string // 消息唯一标识符
  keepAlive?: boolean // 是否保持显示（不自动隐藏）
  closable?: boolean // 是否显示关闭按钮
  actionText?: string // 操作按钮文本
  onActionClick?: () => void // 操作按钮点击回调
}

export interface ConfirmOptions {
  message: string
  confirmText?: string
  cancelText?: string
  duration?: number
}

/**
 * 发送消息到注入的消息组件
 */
export const showMessage = (options: MessageOptions) => {
  const messageData = {
    type: 'FEISHU_EXPORT_MESSAGE',
    message: options.message,
    messageType: options.type || 'info',
    duration: options.duration ?? 3000,
    position: options.position || 'top-center',
    key: options.key,
    keepAlive: options.keepAlive || false,
    closable: options.closable ?? true,
    actionText: options.actionText,
    showConfirm: false
  }

  // 发送消息到当前窗口
  window.postMessage(messageData, '*')
}

/**
 * 移除指定key的消息
 */
export const removeMessage = (key: string) => {
  window.postMessage({
    type: 'FEISHU_EXPORT_REMOVE_MESSAGE',
    key
  }, '*')
}

/**
 * 显示加载消息（类似Toast.loading）
 */
export const showLoading = (options: {
  content: string
  key?: string
  keepAlive?: boolean
  actionText?: string
  onActionClick?: () => void
}) => {
  const messageData = {
    type: 'FEISHU_EXPORT_MESSAGE',
    message: options.content,
    messageType: 'loading',
    duration: 0, // loading默认不自动隐藏
    keepAlive: options.keepAlive ?? true,
    key: options.key,
    actionText: options.actionText,
    showConfirm: !!options.actionText
  }

  window.postMessage(messageData, '*')
}

/**
 * 显示成功消息
 */
export const showSuccess = (message: string, duration = 3000) => {
  showMessage({ message, type: 'success', duration })
}

/**
 * 显示错误消息
 */
export const showError = (message: string, duration = 5000) => {
  showMessage({ message, type: 'error', duration })
}

/**
 * 显示警告消息
 */
export const showWarning = (message: string, duration = 4000) => {
  showMessage({ message, type: 'warning', duration })
}

/**
 * 显示信息消息
 */
export const showInfo = (message: string, duration = 3000) => {
  showMessage({ message, type: 'info', duration })
}

/**
 * 显示确认对话框
 */
export const showConfirm = (options: ConfirmOptions): Promise<boolean> => {
  return new Promise<boolean>((resolve) => {
    const messageId = `confirm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // 监听用户操作结果
    const handleAction = (event: MessageEvent) => {
      if (event.data && event.data.type === 'MESSAGE_ACTION' && event.data.messageId === messageId) {
        window.removeEventListener('message', handleAction)
        resolve(event.data.action === 'confirm')
      }
    }

    window.addEventListener('message', handleAction)

    // 显示确认消息
    window.postMessage({
      type: 'FEISHU_EXPORT_MESSAGE',
      message: options.message,
      messageType: 'info',
      duration: options.duration || 0,
      showConfirm: true,
      confirmButtonText: options.confirmText || '确认',
      cancelButtonText: options.cancelText || '取消',
      key: messageId,
      messageId: messageId
    }, '*')
  })
}

/**
 * Toast兼容层 - 提供与旧Toast系统相同的API
 */
export const Toast = {
  loading: (options: {
    content: string
    key?: string
    keepAlive?: boolean
    actionText?: string
    onActionClick?: () => void
  }) => {
    const messageData = {
      type: 'FEISHU_EXPORT_MESSAGE',
      message: options.content,
      messageType: 'loading',
      duration: 0,
      keepAlive: options.keepAlive ?? true,
      key: options.key,
      actionText: options.actionText,
      showConfirm: !!options.actionText
    }

    // 如果有取消回调，监听取消事件
    if (options.onActionClick && options.key) {
      const handleAction = (event: MessageEvent) => {
        if (event.data && event.data.type === 'MESSAGE_ACTION' &&
          event.data.messageId === options.key) {
          if (event.data.action === 'confirm') {
            options.onActionClick?.()
          }
          window.removeEventListener('message', handleAction)
        }
      }
      window.addEventListener('message', handleAction)
    }

    window.postMessage(messageData, '*')
  },

  success: (options: { content: string; duration?: number }) => {
    showSuccess(options.content, options.duration)
  },

  error: (options: { content: string; actionText?: string; duration?: number; key?: string }) => {
    showMessage({
      message: options.content,
      type: 'error',
      duration: options.duration || 5000,
      key: options.key
    })
  },

  warning: (options: { content: string; duration?: number }) => {
    showWarning(options.content, options.duration)
  },

  info: (options: {
    content: string
    actionText?: string
    onActionClick?: () => void
    onClose?: () => void
    duration?: number
    closable?: boolean
  }) => {
    showMessage({
      message: options.content,
      type: 'info',
      duration: options.duration || 3000,
      actionText: options.actionText,
      closable: options.closable
    })
  },

  remove: (key: string) => {
    removeMessage(key)
  }
}

/**
 * 批量消息管理器
 */
export class MessageManager {
  private messageQueue: MessageOptions[] = []
  private isProcessing = false

  /**
   * 添加消息到队列
   */
  add(options: MessageOptions) {
    this.messageQueue.push(options)
    this.processQueue()
  }

  /**
   * 处理消息队列
   */
  private async processQueue() {
    if (this.isProcessing || this.messageQueue.length === 0) {
      return
    }

    this.isProcessing = true

    while (this.messageQueue.length > 0) {
      const messageOptions = this.messageQueue.shift()!
      showMessage(messageOptions)

      // 等待一段时间再显示下一个消息，避免重叠
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    this.isProcessing = false
  }

  /**
   * 清空消息队列
   */
  clear() {
    this.messageQueue = []
  }
}

// 导出默认消息管理器实例
export const messageManager = new MessageManager()

/**
 * 创建取消控制器辅助函数
 */
export const createCancelController = () => {
  const controller = new AbortController()

  return {
    signal: controller.signal,
    cancel: () => controller.abort(),
    isCancelled: () => controller.signal.aborted
  }
} 