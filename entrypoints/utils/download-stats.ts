import { authStorage } from './auth-client';

/**
 * 操作记录工具类
 * 用于记录用户在系统中的各种操作行为
 */

/**
 * 操作类型枚举
 */
export type OperationType = 
  | '文档查看'
  | '文档下载'
  | 'PDF导出'
  | 'Word导出'
  | 'Markdown导出'
  | 'HTML导出'
  | '图片导出'
  | '复制到飞书'
  | '批量导出';

/**
 * 操作记录参数接口
 */
interface OperationRecordParams {
  operation_time: string;
  operation_type: OperationType;
  document_name: string;
  document_url?: string;
}

/**
 * API响应接口
 */
interface ApiResponse {
  success: boolean;
  message: string;
}

/**
 * 操作记录API客户端
 */
export class OperationStatsClient {
  private baseUrl: string;

  constructor(baseUrl: string = 'https://feishu-export.shenxin.space/api') {
    this.baseUrl = baseUrl;
  }

  /**
   * 记录用户操作
   * @param params 操作记录参数
   * @returns 记录结果
   */
  async recordOperation(params: OperationRecordParams): Promise<ApiResponse> {
    try {
      // 获取认证token
      const token = await authStorage.getToken();

      if (!token) {
        console.warn('未找到认证token，跳过操作统计');
        return { success: false, message: '未登录' };
      }

      const response = await fetch(`${this.baseUrl}/operations/record`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(params)
      });

      // 先解析JSON响应
      const result = await response.json();

      // 如果HTTP状态码不是200，抛出错误并包含后端返回的错误信息
      if (!response.ok) {
        const errorMessage = result.message || `HTTP error! status: ${response.status}`;
        throw new Error(errorMessage);
      }

      return result;
    } catch (error) {
      console.error('操作统计记录失败:', error);
      return { success: false, message: '记录失败' };
    }
  }
}

// 创建默认实例
export const operationStatsClient = new OperationStatsClient();

/**
 * 根据操作类型映射到操作类型
 */
const actionToOperationTypeMap: Record<string, OperationType> = {
  'exportPdf': 'PDF导出',
  'exportCollect': 'PDF导出',
  'exportWord': 'Word导出',
  'exportMd': 'Markdown导出',
  'exportHtml': 'HTML导出',
  'exportImg': '图片导出',
  'copyToFeishu': '复制到飞书',
  'exportPdfAll': '批量导出'
};

/**
 * 记录用户操作的便捷函数
 * 注意：此函数现在主要在background script中使用
 * popup中的调用已经移至background script处理
 * @param action 操作类型
 * @param documentName 文档名称
 * @param documentUrl 可选的文档链接
 */
export const recordUserAction = async (action: string, documentName?: string, documentUrl?: string): Promise<void> => {
  const operationType = actionToOperationTypeMap[action];

  if (!operationType) {
    console.warn(`未知的操作类型: ${action}`);
    return;
  }

  const params: OperationRecordParams = {
    operation_time: new Date().toISOString(),
    operation_type: operationType,
    document_name: documentName || `${action}_${new Date().toISOString()}`,
    document_url: documentUrl
  };

  try {
    const result = await operationStatsClient.recordOperation(params);
    if (result.success) {
      console.log('操作统计记录成功:', action);
    } else {
      console.warn('操作统计记录失败:', result.message);
    }
  } catch (error) {
    console.error('操作统计记录异常:', error);
  }
}; 