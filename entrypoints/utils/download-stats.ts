import { authStorage } from './auth-client';

/**
 * 文件类型枚举
 */
export type FileType = 'PDF' | 'WORD' | 'EXCEL' | 'IMAGE' | 'MARKDOWN' | 'HTML' | 'OTHER';

/**
 * 下载记录参数接口
 */
interface DownloadRecordParams {
  fileType: FileType;
  fileName?: string;
  downloadUrl?: string;
}

/**
 * API响应接口
 */
interface ApiResponse {
  success: boolean;
  message: string;
}

/**
 * 下载记录API客户端
 */
export class DownloadStatsClient {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:3000') {
    this.baseUrl = baseUrl;
  }

  /**
   * 记录下载操作
   * @param params 下载记录参数
   * @returns 记录结果
   */
  async recordDownload(params: DownloadRecordParams): Promise<ApiResponse> {
    try {
      // 获取认证token
      const token = await authStorage.getToken();

      if (!token) {
        console.warn('未找到认证token，跳过下载统计');
        return { success: false, message: '未登录' };
      }

      const response = await fetch(`${this.baseUrl}/downloads/record`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(params)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('下载统计记录失败:', error);
      return { success: false, message: '记录失败' };
    }
  }
}

// 创建默认实例
export const downloadStatsClient = new DownloadStatsClient();

/**
 * 根据操作类型映射到文件类型
 */
const actionToFileTypeMap: Record<string, FileType> = {
  'exportPdf': 'PDF',
  'exportCollect': 'PDF',
  'exportWord': 'WORD',
  'exportMd': 'MARKDOWN',
  'exportHtml': 'HTML',
  'exportImg': 'IMAGE',
  'copyToFeishu': 'OTHER',
  'exportPdfAll': 'PDF'
};

/**
 * 记录用户操作的便捷函数
 * 注意：此函数现在主要在background script中使用
 * popup中的调用已经移至background script处理
 * @param action 操作类型
 * @param fileName 可选的文件名
 * @param downloadUrl 可选的下载链接
 */
export const recordUserAction = async (action: string, fileName?: string, downloadUrl?: string): Promise<void> => {
  const fileType = actionToFileTypeMap[action];

  if (!fileType) {
    console.warn(`未知的操作类型: ${action}`);
    return;
  }

  const params: DownloadRecordParams = {
    fileType,
    fileName: fileName || `${action}_${new Date().toISOString()}`,
    downloadUrl: downloadUrl
  };

  try {
    const result = await downloadStatsClient.recordDownload(params);
    if (result.success) {
      console.log('下载统计记录成功:', action);
    } else {
      console.warn('下载统计记录失败:', result.message);
    }
  } catch (error) {
    console.error('下载统计记录异常:', error);
  }
}; 