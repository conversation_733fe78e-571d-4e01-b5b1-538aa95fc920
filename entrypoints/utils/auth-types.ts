/**
 * 认证相关的TypeScript类型定义
 */

// API响应的基础类型
export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  error?: string
}

// 发送验证码的响应类型
export interface SendCodeResponse extends ApiResponse {
  // 可能包含其他字段，如验证码过期时间等
}

// 用户信息类型
export interface User {
  id: string | number
  email: string
  name?: string
  avatar?: string
  createdAt?: string
  updatedAt?: string
  // 可以根据实际需要添加更多字段
}

// 验证码登录的响应类型
export interface VerifyCodeResponse extends ApiResponse {
  token?: string
  user?: User
}

// 获取用户资料的响应类型
export interface ProfileResponse extends ApiResponse {
  user?: User
}

// 登录状态类型
export interface LoginState {
  isLoading: boolean
  isSendingCode: boolean
  codeSent: boolean
  countdown: number
  errors: {
    email: string
    code: string
    general: string
  }
}

// 表单数据类型
export interface LoginFormData {
  email: string
  code: string
}

// 认证客户端配置类型
export interface AuthClientConfig {
  baseUrl?: string
  timeout?: number
  retryTimes?: number
}

// 错误类型
export interface AuthError {
  code?: string
  message: string
  details?: any
}

// 验证结果类型
export interface ValidationResult {
  isValid: boolean
  message?: string
}

// 邮箱验证结果
export interface EmailValidationResult extends ValidationResult { }

// 验证码验证结果
export interface CodeValidationResult extends ValidationResult { }

// Chrome扩展认证消息类型
export interface AuthMessage {
  action: 'getAuthToken' | 'setAuthToken' | 'clearAuthToken' | 'checkAuthStatus'
  token?: string
}

// Chrome扩展认证响应类型
export interface AuthMessageResponse {
  success: boolean
  token?: string | null
  isLoggedIn?: boolean
  error?: string
}

// Token变化监听器类型
export type TokenChangeCallback = (token: string | null) => void

// Chrome扩展存储接口
export interface AuthStorage {
  setToken(token: string): Promise<void>
  getToken(): Promise<string | null>
  clearToken(): Promise<void>
  isLoggedIn(): Promise<boolean>
  onTokenChange(callback: TokenChangeCallback): void
} 