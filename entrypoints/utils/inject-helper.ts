export const executeScriptByFlag = async (tabId: number, exportType: string, options: any = { quality: 'high' }) => {
  // 确保所有参数都是可序列化的
  const serializableType = String(exportType);
  const serializableOptions = JSON.parse(JSON.stringify(options));

  await browser.scripting.executeScript({
    target: { tabId },
    func: (type, opts) => {
      // 在页面中设置全局变量，供注入的脚本使用
      window.exportConfig = { type, opts };
    },
    args: [serializableType, serializableOptions],
    world: 'MAIN',
  });
  await browser.scripting.executeScript({
    files: ['/injected.js'],
    target: { tabId },
    world: 'MAIN',
  });
}
