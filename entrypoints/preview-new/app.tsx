import { createComponent } from '@/pkg/utils/create-component'
import { logger } from '@/pkg/utils/logger'
import { getTodosRepo } from '../utils/service'
import { ref, onMounted } from 'vue'
import { DocNode } from './types'
import { PageRenderer } from './components/PageRenderer'

export const App = createComponent({
  props: {
  },
}, props => {
  const previewData = ref<DocNode | null>(null)
  const pageTitle = ref<string>('')
  const originalUrl = ref<string>('')

  onMounted(async () => {
    const todosRepo = getTodosRepo();
    const res = await todosRepo.getAll();
    const preview = res.find(item => item.id === 'collectData');
    if (preview) {
      logger.log('preview', preview.data)
      // 将数据转换为DocNode类型
      previewData.value = preview.data as unknown as DocNode
      pageTitle.value = preview.title || '无标题文档'
      originalUrl.value = preview.originalUrl as unknown as string
      // 设置页面标题
      document.title = pageTitle.value + ' - 飞书转存专家'
    }
  })

  const handlePrint = () => {
    window.print();
  };

  const currentTime = new Date().toLocaleString("zh-CN");

  return () => (
    <div class="flex min-h-screen">
      {/* 左侧提示和操作区域 */}
      <div class="w-80 bg-gray-50 text-gray-800 fixed left-0 top-0 h-screen overflow-y-auto z-10 shadow-md flex flex-col print:hidden">
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center gap-3">
            <div class="text-3xl bg-gray-100 w-12 h-12 rounded-xl flex items-center justify-center">📄</div>
            <div class="flex-1">
              <h1 class="text-xl font-bold text-gray-800 m-0">飞书转存专家</h1>
              <p class="text-sm text-gray-500 mt-1 m-0">强大的飞书（lark）文档转存、导出工具，一键创建文档副本到个人空间，支持PDF、Word、Markdown导出</p>
            </div>
          </div>
        </div>

        <div class="p-5 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-800 m-0 mb-2 break-words leading-tight">{pageTitle.value || '文档预览'}</h2>
          <p class="text-xs text-gray-500 m-0">{currentTime}</p>
        </div>

        <div class="p-5 border-b border-gray-200">
          <button
            onClick={handlePrint}
            class="w-full bg-[#2d7bf9] text-white py-3 px-4 text-sm font-semibold cursor-pointer flex items-center justify-center gap-2 rounded-lg transition-all duration-300 hover:bg-blue-500 hover:-translate-y-0.5 hover:shadow-lg mb-3"
          >
            <span class="text-base">📄</span>
            <span>保存为PDF</span>
          </button>
        </div>

        <div class="p-5 border-b border-gray-200">
          <h3 class="text-base font-semibold text-gray-800 m-0 mb-4 flex items-center gap-2">📋 使用指南</h3>
          <div class="flex flex-col gap-3">
            <div class="flex items-start gap-3">
              <div class="bg-gray-200 text-gray-700 w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold flex-shrink-0">1</div>
              <div class="flex-1 flex flex-col gap-0.5">
                <span class="text-sm font-medium text-gray-800">点击保存为PDF</span>
                <span class="text-xs text-gray-500">开始导出流程</span>
              </div>
            </div>

            <div class="flex items-start gap-3">
              <div class="bg-gray-200 text-gray-700 w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold flex-shrink-0">2</div>
              <div class="flex-1 flex flex-col gap-0.5">
                <span class="text-sm font-medium text-gray-800">选择保存位置</span>
                <span class="text-xs text-gray-500">点击"保存"完成导出</span>
              </div>
            </div>
          </div>
        </div>

        <div class="p-5 mt-auto">
          <div class="mb-4">
            <span class="block text-xs text-gray-500 mb-1">原文地址</span>
            <a href={originalUrl.value} target="_blank" class="text-gray-700 no-underline text-xs break-all transition-colors duration-300 hover:text-blue-600 hover:underline">{originalUrl.value}</a>
          </div>
          <div class="bg-gray-100 p-3 rounded-lg">
            <p class="text-xs text-gray-600 text-center pb-2.5 m-0">觉得有用？请给个五星好评 ⭐</p>
            <a
              href="https://chromewebstore.google.com/detail/%E9%A3%9E%E4%B9%A6%E5%8A%A9%E6%89%8B-%E9%A3%9E%E4%B9%A6lark%E5%AF%BC%E5%87%BA/cfenjfhlhjpkaaobmhbobajnnhifilbl"
              target="_blank"
              class="block text-white no-underline text-xs font-medium text-center py-1.5 px-3 bg-[#007AFF] rounded-md transition-all duration-300 hover:bg-[#007AFF] hover:-translate-y-0.5"
            >
              前往Chrome商店评分
            </a>
          </div>
        </div>
      </div>

      {/* 右侧文档内容区域 */}
      <div class="flex-1 ml-80 bg-white min-h-screen print:ml-0">
        <div class="bg-white p-10 min-h-screen">
          <div class="max-w-[840px] mx-auto mb-10 text-center border-b border-gray-200 pb-8 print:m-0 print:border-b print:border-gray-300 print:pb-5">
            <h1 class="text-4xl font-bold text-slate-800 m-0 mb-3 leading-snug">{pageTitle.value || '文档标题'}</h1>
           </div>
          <div class="max-w-[840px] mx-auto text-base leading-relaxed text-slate-800 print:max-w-none print:m-0 print:text-black">
            {previewData.value ? (
              <PageRenderer node={previewData.value} />
            ) : (
              <div class="flex justify-center items-center h-64">
                <p class="text-gray-500">加载中...</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
})
