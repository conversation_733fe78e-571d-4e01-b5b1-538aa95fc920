// 定义飞书文档数据的类型
export interface TextAttribute {
  author?: string
  bold?: string
  strikethrough?: string
  underline?: string
  inlineCode?: string
  textHighlight?: string
  textHighlightBackground?: string
  fixEnter?: string
  link?: string
  'clientside--todo-line-through'?: string
  'inline-component'?: string
  'link-id'?: string
}

export interface TextOp {
  insert: string
  attributes?: TextAttribute
}

export interface ZoneState {
  allText: string
  content: {
    ops: TextOp[]
  }
}

// 图片的Caption类型
export interface ImageCaption {
  text: {
    apool: {
      nextNum: number
      numToAttrib: any
    }
    initialAttributedTexts: {
      attribs: any
      text: string[] | null
    }
  }
}

// 白板的Caption类型（与图片类似）
export interface WhiteboardCaption {
  text: {
    initialAttributedTexts: {
      text: string[] | null
    }
  }
}

// 图片类型定义
export interface ImageData {
  token: string
  width: number
  height: number
  mimeType: string
  name: string
  scale?: number
  caption: ImageCaption
}

// 文件类型定义
export interface FileData {
  token: string
  name: string
  size?: number
}

// 扩展快照类型以支持不同节点类型
export interface DocSnapshot {
  type: string
  seq?: string
  done?: boolean
  rows_id?: string[]
  columns_id?: string[]
  // 图片特有属性
  image?: ImageData
  // 文件特有属性
  file?: FileData
  // 白板特有属性
  caption?: WhiteboardCaption
  // Callout 特有属性
  emoji_id?: string
  emoji_value?: string
  background_color?: string
  border_color?: string
  text_color?: string
  align?: string
  // 代码块特有属性（fallback 类型）
  language?: string
  text?: {
    apool?: {
      nextNum: number
      numToAttrib: any
    }
    initialAttributedTexts?: {
      attribs?: any
      text?: string[]
    }
  }
  folded?: boolean
  wrap?: boolean
}

export interface DocNode {
  id: number
  type: string
  zoneState?: ZoneState
  snapshot: DocSnapshot
  record: {
    id: string
  }
  children?: DocNode[]
  language?: string
  // 白板特有属性
  whiteboardImageData?: string  // base64 dataURL
  hasWhiteboardData?: boolean
}

// 用于分组列表项的类型定义
export interface ListGroup {
  type: 'ordered' | 'bullet'
  items: DocNode[]
}

// 渲染项可能是一个文档节点或列表分组
export type RenderItem = DocNode | ListGroup 