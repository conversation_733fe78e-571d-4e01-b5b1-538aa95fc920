import { DocNode } from '../types'
import { onMounted, ref } from 'vue'
import { codeToHtml } from 'shiki'
import { createComponent, required } from '@/pkg/utils/create-component'

type CodeRendererProps = {
  props: {
    node: DocNode
  }
}
export const CodeRenderer = createComponent<CodeRendererProps>({
  props: {
    node: required,
  },
}, props => {
  const { node } = props
  const highlightedCode = ref<string>('')
  const isLoading = ref<boolean>(true)

  // 获取代码内容和语言，支持从 zoneState 或 snapshot 中获取
  let codeContent = ''
  let language = 'plaintext'

  if (node.zoneState && node.zoneState.content) {
    // 从 zoneState 获取代码内容（原有逻辑）
    codeContent = node.zoneState.content.ops
      .map(op => op.insert)
      .join('')
      .replace(/\n$/, '') // 移除末尾的换行符
    language = node.language || 'plaintext'
  } else if (node.snapshot && node.snapshot.type === 'code') {
    // 从 snapshot 获取代码内容（fallback 类型）
    const snapshotData = node.snapshot as any
    if (snapshotData.text && snapshotData.text.initialAttributedTexts && snapshotData.text.initialAttributedTexts.text) {
      codeContent = snapshotData.text.initialAttributedTexts.text[0] || ''
    }
    language = snapshotData.language || 'plaintext'
  }

  if (!codeContent) {
    return () => <div class="my-4 p-4 bg-gray-100 dark:bg-gray-900 rounded-lg text-gray-500 dark:text-gray-400">空代码块</div>
  }

  onMounted(async () => {
    try {
      // 使用 shiki 提供的 codeToHtml 方法进行高亮，同时支持浅色和深色主题
      highlightedCode.value = await codeToHtml(codeContent, {
        lang: 'ts',
        theme: 'github-light',
        transformers: [
          {
            name: 'line-wrapping',
            line(node) {
              return {
                ...node,
                properties: { ...node.properties, style: 'white-space: pre-wrap;' }
              }
            }
          }
        ]
      })

      isLoading.value = false
    } catch (error) {
      console.error('代码高亮出错:', error)
      isLoading.value = false
    }
  })
  return () => (
    (
      <div class="my-4 rounded-lg overflow-hidden">
        {/* <div class="bg-gray-200 text-xs px-4 py-1.5 text-gray-800 border-b border-gray-300 dark:border-gray-700 flex items-center print:relative print:w-full">
          <span>{language}</span>
        </div> */}
        {isLoading.value ? (
          <pre class="p-4 bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 overflow-x-auto">
            <code>{codeContent}</code>
          </pre>
        ) : (
          <div
            class="shiki-container"
            innerHTML={highlightedCode.value}
            style={{
              margin: 0,
              padding: 0
            }}
          />
        )}
      </div>
    )
  )
})