import { DocNode } from '../types'
import { createComponent, required } from '@/pkg/utils/create-component'
import { TextStyleRenderer } from './TextStyleRenderer'
import { groupAdjacentLists } from '../utils/list-grouping'
import { ListGroupRenderer } from './ListGroupRenderer'

type QuoteRendererProps = {
  props: {
    node: DocNode
  }
}

export const QuoteRenderer = createComponent<QuoteRendererProps>({
  props: {
    node: required,
  },
}, props => {
  const { node } = props

  return () => {
    // 如果有子节点，使用工具函数处理列表分组
    if (node.children && node.children.length > 0) {
      const processedChildren = groupAdjacentLists(node.children)

      return (
        <div class="py-1 border-l-2 text-gray-600 border-gray-300 pl-2 rounded-r">
          <ListGroupRenderer items={processedChildren} keyPrefix="quote" />
        </div>
      )
    } else if (node.zoneState?.content?.ops) {
      // 如果没有子节点但有文本内容
      return (
        <div class="py-1 border-l-2 text-gray-600 border-gray-300 pl-2 rounded-r">
          <p>
            {node.zoneState.content.ops.map((op, index) => (
              <TextStyleRenderer key={index} op={op} />
            ))}
          </p>
        </div>
      )
    } else {
      // 完全没有内容
      return (
        <div class="py-1 border-l-2 text-gray-600 border-gray-300 pl-2 rounded-r">
          <p>{node.zoneState?.allText || ''}</p>
        </div>
      )
    }
  }
}) 