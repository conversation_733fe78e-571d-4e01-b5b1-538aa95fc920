import { DocNode } from '../types'
import { TextNodeRenderer } from './TextNodeRenderer'
import { ImageRenderer } from './ImageRenderer'
import { CodeRenderer } from './CodeRenderer'
import { TodoRenderer } from './TodoRenderer'
import { Heading<PERSON>enderer } from './HeadingRenderer'
import { Quote<PERSON>enderer } from './QuoteRenderer'
import { DividerRenderer } from './DividerRenderer'
import { FileRenderer } from './FileRenderer'
import { CalloutRenderer } from './CalloutRenderer'
import { ListItemRenderer } from './ListItemRenderer'
import { BulletListRenderer } from './BulletListRenderer'
import { OrderedListRenderer } from './OrderedListRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type ContainerContentRendererProps = {
  props: {
    children: DocNode[]
    compact?: boolean // 是否使用紧凑样式（如表格中的内容）
  }
}

// 列表分组类型定义
interface ListGroup {
  type: 'list' | 'other'
  listType?: string
  blocks: DocNode[]
}

export const ContainerContentRenderer = createComponent<ContainerContentRendererProps>({
  props: {
    children: required,
    compact: false,
  },
}, props => {
  const { children, compact = false } = props

  // 对连续的列表块进行分组（参考PDF导出实现）
  const groupConsecutiveListBlocks = (blocks: DocNode[]): ListGroup[] => {
    const groups: ListGroup[] = []
    let currentGroup: ListGroup | null = null

    for (const block of blocks) {
      const isListBlock = block.type === 'bullet' ||
        block.type === 'ordered' ||
        block.type === 'todo'

      if (isListBlock) {
        if (!currentGroup || currentGroup.type !== 'list' || currentGroup.listType !== block.type) {
          // 开始新的列表组
          currentGroup = {
            type: 'list',
            listType: block.type,
            blocks: [block]
          }
          groups.push(currentGroup)
        } else {
          // 添加到当前列表组
          currentGroup.blocks.push(block)
        }
      } else {
        if (!currentGroup || currentGroup.type !== 'other') {
          // 开始新的非列表组
          currentGroup = {
            type: 'other',
            blocks: [block]
          }
          groups.push(currentGroup)
        } else {
          // 添加到当前非列表组
          currentGroup.blocks.push(block)
        }
      }
    }

    return groups
  }

  // 渲染单个节点
  const renderSingleNode = (node: DocNode) => {
    const key = node.id.toString()

    switch (node.type) {
      case 'text':
        return <TextNodeRenderer key={key} node={node} />
      case 'image':
        return <ImageRenderer key={key} node={node} />
      case 'code':
        return <CodeRenderer key={key} node={node} />
      case 'todo':
        return <TodoRenderer key={key} node={node} />
      case 'heading1':
      case 'heading2':
      case 'heading3':
      case 'heading4':
      case 'heading5':
      case 'heading6':
        return <HeadingRenderer key={key} node={node} />
      case 'quote_container':
      case 'quote':
        return <QuoteRenderer key={key} node={node} />
      case 'divider':
        return <DividerRenderer key={key} node={node} />
      case 'file':
        return <FileRenderer key={key} node={node} />
      case 'callout':
        return <CalloutRenderer key={key} node={node} />
      default:
        // 对于不支持的类型，显示友好提示
        return (
          <div key={key} class="text-gray-500 text-sm italic">
            不支持的内容类型: {node.type}
          </div>
        )
    }
  }

  // 渲染列表组
  const renderListGroup = (group: ListGroup, groupIndex: number) => {
    if (group.type !== 'list' || !group.listType) {
      return null
    }

    const key = `list-group-${groupIndex}`
    const listStyle = compact ? { margin: '4px 0', paddingLeft: '16px' } : undefined

    switch (group.listType) {
      case 'bullet':
        return (
          <div key={key} style={listStyle}>
            <BulletListRenderer items={group.blocks} />
          </div>
        )
      case 'ordered':
        return (
          <div key={key} style={listStyle}>
            <OrderedListRenderer items={group.blocks} level={0} />
          </div>
        )
      case 'todo':
        // Todo 列表不分组，直接逐个渲染
        return (
          <div key={key} style={compact ? { margin: '4px 0' } : undefined}>
            {group.blocks.map(block => renderSingleNode(block))}
          </div>
        )
      default:
        return null
    }
  }

  // 包装内容（为不同容器环境添加适当样式）
  const wrapContent = (content: any, blockType: string) => {
    if (compact) {
      // 紧凑模式下，减少边距
      const style = {
        margin: blockType === 'text' ? '2px 0' : '4px 0'
      }
      return <div style={style}>{content}</div>
    }
    return content
  }

  return () => {
    if (!children || children.length === 0) {
      return null
    }

    const groupedChildren = groupConsecutiveListBlocks(children)
    const renderedContent: any[] = []

    groupedChildren.forEach((group, groupIndex) => {
      if (group.type === 'list' && group.listType) {
        // 渲染列表组
        const listElement = renderListGroup(group, groupIndex)
        if (listElement) {
          renderedContent.push(listElement)
        }
      } else {
        // 渲染单个非列表块
        group.blocks.forEach(block => {
          const element = renderSingleNode(block)
          if (element) {
            const wrappedElement = wrapContent(element, block.type)
            renderedContent.push(wrappedElement)
          }
        })
      }
    })

    return <div>{renderedContent}</div>
  }
}) 