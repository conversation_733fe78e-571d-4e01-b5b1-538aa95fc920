import { DocNode } from '../types'
import { ContainerContentRenderer } from './ContainerContentRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type GridColumnRendererProps = {
  props: {
    node: DocNode
  }
}

export const GridColumnRenderer = createComponent<GridColumnRendererProps>({
  props: {
    node: required,
  },
}, props => {
  const { node } = props

  return () => {
    if (!node.children || node.children.length === 0) {
      return null
    }

    // 对于只包含单个图片的列，应用特殊样式
    const hasOnlyImage = node.children.length === 1 && node.children[0].type === 'image'

    return (
      <div class={`flex flex-col ${hasOnlyImage ? 'h-full' : ''}`}>
        <ContainerContentRenderer
          children={node.children}
          compact={false}
        />
      </div>
    )
  }
}) 