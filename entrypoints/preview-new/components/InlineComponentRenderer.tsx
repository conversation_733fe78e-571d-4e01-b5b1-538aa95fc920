import { createComponent, required } from '@/pkg/utils/create-component'

interface MentionDocData {
  type: 'mention_doc'
  data: {
    title: string
    raw_url: string
    icon_info?: {
      key: string
    }
  }
}

interface ReminderData {
  type: 'reminder'
  data: {
    is_whole_day: boolean
    should_notify: boolean
    notify_time: number
    expire_time: number
    create_user_id: string
  }
}

interface TextOp {
  insert: string
  attributes?: Record<string, any>
}

type InlineComponentRendererProps = {
  props: {
    op: TextOp
  }
}

export const InlineComponentRenderer = createComponent<InlineComponentRendererProps>({
  props: {
    op: required,
  },
}, props => {
  const { op } = props

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return () => {
    const attrs = op.attributes || {}

    if (attrs['inline-component']) {
      try {
        const componentData: MentionDocData | ReminderData = JSON.parse(attrs['inline-component'])

        if (componentData.type === 'mention_doc') {
          const { data } = componentData
          const emoji = data.icon_info?.key ? String.fromCodePoint(parseInt(data.icon_info.key, 16)) : '📄'

          return (
            <a
              href={data.raw_url}
              target="_blank"
              rel="noopener noreferrer"
              class="inline-flex items-center gap-1 text-base text-[#646a73] hover:underline hover:text-blue-800 transition-colors"
            >
              <span class="">{emoji}</span>
              <span class="">{data.title}</span>
            </a>
          )
        } else if (componentData.type === 'reminder') {
          const { data } = componentData as ReminderData
          return (
            <span class="inline-flex items-center gap-1 text-base text-[#646a73]">
              <span class="text-lg">⏰</span>
              <span>{formatDate(data.notify_time)}</span>
            </span>
          )
        }
      } catch (error) {
        console.warn('Failed to parse inline-component:', error)
      }
    }

    // 如果没有内联组件或解析失败，返回原始文本
    return <span>{op.insert}</span>
  }
}) 