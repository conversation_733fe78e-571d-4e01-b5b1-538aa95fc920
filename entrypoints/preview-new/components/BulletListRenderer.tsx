import { DocNode } from '../types'
import { ListItemRenderer } from './ListItemRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type BulletListRendererProps = {
  props: {
    items: DocNode[]
  }
}

export const BulletListRenderer = createComponent<BulletListRendererProps>({
  props: {
    items: required,
  },
}, props => {
  const { items } = props

  return () => (
    <ul>
      {items.map((node, idx) => (
        <ListItemRenderer key={idx} node={node} />
      ))}
    </ul>
  )
}) 