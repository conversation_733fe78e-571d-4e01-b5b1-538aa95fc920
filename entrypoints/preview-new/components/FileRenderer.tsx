import { DocNode } from '../types'
import { createComponent, required } from '@/pkg/utils/create-component'

type FileRendererProps = {
  props: {
    node: DocNode
  }
}

export const FileRenderer = createComponent<FileRendererProps>({
  props: {
    node: required,
  }
}, props => {
  const { node } = props

  // 构建文件下载链接
  const handleDownload = () => {
    if (node.snapshot?.file?.token) {
      const token = node.snapshot.file.token
      const name = node.snapshot.file.name || "未命名文件"
      const downloadUrl = `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/preview/${token}?mount_point=docx_file&preview_type=16`

      // 创建一个临时链接并模拟点击以触发下载
      const link = document.createElement('a')
      link.href = downloadUrl
      link.target = '_blank'
      link.download = name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }



  // 格式化文件大小
  const formatFileSize = (size?: number): string => {
    if (!size) return ''

    if (size < 1024) {
      return `${size}B`
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(2)}KB`
    } else if (size < 1024 * 1024 * 1024) {
      return `${(size / (1024 * 1024)).toFixed(2)}MB`
    } else {
      return `${(size / (1024 * 1024 * 1024)).toFixed(2)}GB`
    }
  }

  return () => {
    if (!node.snapshot?.file) {
      return <div class="text-red-500">文件信息不完整</div>
    }

    const { name, token, size } = node.snapshot.file

    // 如果没有文件大小信息，根据文件名长度模拟一个大小
    let displaySize = size
    if (!displaySize && name) {
      // 简单模拟：文件名每个字符算0.5KB，最少1KB
      displaySize = Math.max(1024, name.length * 512)
    }

    const formattedSize = formatFileSize(displaySize)

    return (
      <div class="my-4">
        <div
          class="flex p-2 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 cursor-pointer transition-colors w-[400px]"
          onClick={handleDownload}
        >
          <div class="flex items-center w-full">
            <div class="mr-4">
              <svg width="48" height="48" viewBox="0 0 32 32">
                <defs>
                  <path
                    d="M1.5 0h14.086a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V26.5a1.5 1.5 0 01-1.5 1.5h-19A1.5 1.5 0 010 26.5v-25A1.5 1.5 0 011.5 0z"
                    id="icon_file_zip_nor_svg__a"></path>
                  <path d="M16.293.293l5.414 5.414A1 1 0 0121.91 6H17.5A1.5 1.5 0 0116 4.5V.09a1 1 0 01.293.203z"
                    id="icon_file_zip_nor_svg__b"></path>
                </defs>
                <g fill="none" fill-rule="evenodd">
                  <g transform="translate(5 2)">
                    <use fill="#3370FF" xlink:href="#icon_file_zip_nor_svg__a"></use>
                    <use fill="#245BDB" xlink:href="#icon_file_zip_nor_svg__b"></use>
                  </g>
                  <path d="M8 11h16.649v16.649H8z"></path>
                  <path
                    d="M20 13a1 1 0 011 1v10a1 1 0 01-1 1h-8a1 1 0 01-1-1V14a1 1 0 011-1h8zm-2.7 5.5h-1.1a.2.2 0 00-.2.2v1.1c0 .*********.2h1.1a.2.2 0 00.2-.2v-1.1a.2.2 0 00-.2-.2zM15.8 17h-1.1a.2.2 0 00-.193.147l-.007.053v1.1a.2.2 0 00.147.193l.053.007h1.1a.2.2 0 00.193-.147L16 18.3v-1.1a.2.2 0 00-.2-.2zm1.5-1.5h-1.1a.2.2 0 00-.193.147L16 15.7v1.1a.2.2 0 00.147.193L16.2 17h1.1a.2.2 0 00.193-.147l.007-.053v-1.1a.2.2 0 00-.2-.2zM15.8 14h-1.1a.2.2 0 00-.193.147l-.007.053v1.1a.2.2 0 00.147.193l.053.007h1.1a.2.2 0 00.193-.147L16 15.3v-1.1a.2.2 0 00-.2-.2zm-1.6 7h3.6c.11 0 .2.09.2.2v2.6a.2.2 0 01-.2.2h-3.6a.2.2 0 01-.2-.2v-2.6c0-.11.09-.2.2-.2zm.8 2h2v-1h-2v1z"
                    fill="#FFF"></path>
                </g>
              </svg>
            </div>
            <div class="flex flex-col justify-center">
              <div class="text-gray-900 font-medium">{name}</div>
              {formattedSize && <div class="text-gray-500 text-sm">{formattedSize}</div>}
            </div>
            <div class="text-gray-500 text-sm ml-auto mr-4">点击下载</div>
          </div>
        </div>
      </div>
    )
  }
}) 