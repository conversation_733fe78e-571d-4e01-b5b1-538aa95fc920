import { DocNode } from '../types'
import { ref, onMounted } from 'vue'
import { createComponent, required } from '@/pkg/utils/create-component'

type WhiteboardRendererProps = {
  props: {
    node: DocNode
  }
}

export const WhiteboardRenderer = createComponent<WhiteboardRendererProps>({
  props: {
    node: required,
  }
}, props => {
  const { node } = props
  const loading = ref(true)
  const error = ref(false)

  // 获取白板标题
  const whiteboardTitle = node.snapshot?.caption?.text?.initialAttributedTexts?.text?.[0] || '白板'

  onMounted(() => {
    // 如果有白板图片数据，直接显示
    if (node.whiteboardImageData) {
      loading.value = false
    } else {
      error.value = true
      loading.value = false
    }
  })

  // 处理图片加载完成
  const handleLoad = () => {
    loading.value = false
  }

  // 处理图片加载失败
  const handleError = () => {
    error.value = true
    loading.value = false
  }

  return () => (
    <div class="my-4">
      {loading.value && !error.value && (
        <div class="flex justify-center items-center h-64 bg-gray-100 rounded">
          <p class="text-gray-500">白板加载中...</p>
        </div>
      )}

      {error.value && (
        <div class="flex justify-center items-center min-h-48 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300">
          <div class="text-center">
            <div class="text-4xl mb-2">🎨</div>
            <div class="text-lg font-medium text-gray-700 mb-1">白板: {whiteboardTitle}</div>
            <div class="text-sm text-gray-500">
              {node.hasWhiteboardData ? '白板数据不可用' : '白板内容需要在飞书文档中查看'}
            </div>
          </div>
        </div>
      )}

      {node.whiteboardImageData && (
        <div class="text-center">
          <img
            src={node.whiteboardImageData}
            alt={whiteboardTitle}
            class={`max-w-full rounded shadow-md ${loading.value ? 'hidden' : ''}`}
            onLoad={handleLoad}
            onError={handleError}
            style={{
              maxHeight: '600px',
              margin: '0 auto'
            }}
          />
          
        </div>
      )}
    </div>
  )
}) 