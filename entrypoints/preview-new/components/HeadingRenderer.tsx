import { DocNode } from '../types'
import { TextStyleRenderer } from './TextStyleRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type HeadingRendererProps = {
  props: {
    node: DocNode
  }
}

export const HeadingRenderer = createComponent<HeadingRendererProps>({
  props: {
    node: required,
  },
}, props => {
  const { node } = props

  return () => {
    if (!node.zoneState || !node.zoneState.content) {
      return null
    }

    const textContent = node.zoneState.content.ops.map((op, idx) => {
      if (op.insert === '\n') return null
      return <span key={idx}><TextStyleRenderer op={op} /></span>
    })

    // 根据标题类型选择不同的样式
    switch (node.type) {
      case 'heading1':
        return <h1 class="text-3xl font-bold mt-6 mb-3 text-gray-900 dark:text-gray-100">{textContent}</h1>
      case 'heading2':
        return <h2 class="text-2xl font-bold mt-5 mb-2 text-gray-800 dark:text-gray-200  ">{textContent}</h2>
      case 'heading3':
        return <h3 class="text-xl font-bold mt-4 mb-2 text-gray-800 dark:text-gray-200">{textContent}</h3>
      case 'heading4':
        return <h4 class="text-lg font-bold mt-3 mb-2 text-gray-700 dark:text-gray-300">{textContent}</h4>
      case 'heading5':
        return <h5 class="text-base font-bold mt-3 mb-1 text-gray-700 dark:text-gray-300">{textContent}</h5>
      case 'heading6':
        return <h6 class="text-sm font-bold mt-2 mb-1 text-gray-600 dark:text-gray-400">{textContent}</h6>
      default:
        return <h1 class="text-xl font-bold my-2 text-gray-900 dark:text-gray-100">{textContent}</h1>
    }
  }
}) 