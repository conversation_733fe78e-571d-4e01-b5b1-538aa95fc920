import { DocNode } from '../types'
import { GridColumnRenderer } from './GridColumnRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type GridRendererProps = {
  props: {
    node: DocNode
  }
}

export const Grid<PERSON>enderer = createComponent<GridRendererProps>({
  props: {
    node: required,
  },
}, props => {
  const { node } = props

  return () => {
    if (!node.children || node.children.length === 0) {
      return null
    }

    // 获取有效的grid_column子节点
    const columns = node.children.filter(child => child.type === 'grid_column')

    return (
      <div class="flex my-2">
        {columns.map((column) => (
          <div key={column.id} class="flex-1 rounded overflow-hidden mx-1">
            <GridColumnRenderer node={column} />
          </div>
        ))}
      </div>
    )
  }
}) 