import { DocNode } from '../types'
import { groupAdjacentLists } from '../utils/list-grouping'
import { ListGroupRenderer } from './ListGroupRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type PageRendererProps = {
  props: {
    node: DocNode
  }
}

export const PageRenderer = createComponent<PageRendererProps>({
  props: {
    node: required,
  },
}, props => {
  const { node } = props

  return () => {
    if (!node.children) {
      return <div class="bg-white"></div>
    }

    // 使用工具函数处理列表分组
    const processedChildren = groupAdjacentLists(node.children)

    return (
      <div class="bg-white">
        <ListGroupRenderer items={processedChildren} keyPrefix="page" />
      </div>
    )
  }
}) 