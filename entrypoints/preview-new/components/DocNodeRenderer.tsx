import { DocNode } from '../types'
import { TextNode<PERSON>enderer } from './TextNodeRenderer'
import { ListItemRenderer } from './ListItemRenderer'
import { CodeRenderer } from './CodeRenderer'
import { QuoteRenderer } from './QuoteRenderer'
import { Divider<PERSON>enderer } from './DividerRenderer'
import { TodoRenderer } from './TodoRenderer'
import { TableRenderer } from './TableRenderer'
import { TableCellRenderer } from './TableCellRenderer'
import { CalloutRenderer } from './CalloutRenderer'
import { ImageRenderer } from './ImageRenderer'
import { FileRenderer } from './FileRenderer'
import { HeadingRenderer } from './HeadingRenderer'
import { GridRenderer } from './GridRenderer'
import { WhiteboardRenderer } from './WhiteboardRenderer'
import { ContainerContentRenderer } from './ContainerContentRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type DocNodeRendererProps = {
  props: {
    node: DocNode
  }
}

export const DocNodeRenderer = createComponent<DocNodeRendererProps>({
  props: {
    node: required,
  },
}, props => {
  const { node } = props

  return () => {
    // 处理 fallback 类型，使用 snapshot 中的真正类型
    if (node.type === 'fallback' && node.snapshot) {
      const actualNode: DocNode = {
        ...node,
        type: node.snapshot.type
      }
      return <DocNodeRenderer node={actualNode} />
    }

    if (node.type === 'page') {
      // 页面节点应该使用PageRenderer组件，但为了避免循环引用，在App.tsx中处理
      return <div>页面节点不应直接使用DocNodeRenderer渲染</div>
    } else if (node.type === 'text') {
      return <TextNodeRenderer node={node} />
    } else if (node.type === 'ordered' || node.type === 'bullet') {
      return <ListItemRenderer node={node} />
    } else if (node.type === 'code') {
      return <CodeRenderer node={node} />
    } else if (node.type === 'quote_container' || node.type === 'quote') {
      return <QuoteRenderer node={node} />
    } else if (node.type === 'divider') {
      return <DividerRenderer node={node} />
    } else if (node.type === 'todo') {
      return <TodoRenderer node={node} />
    } else if (node.type === 'table') {
      return <TableRenderer node={node} />
    } else if (node.type === 'table_cell') {
      return <TableCellRenderer node={node} />
    } else if (node.type === 'callout') {
      return <CalloutRenderer node={node} />
    } else if (node.type === 'image') {
      return <ImageRenderer node={node} />
    } else if (node.type === 'file') {
      return <FileRenderer node={node} />
    } else if (node.type === 'whiteboard') {
      return <WhiteboardRenderer node={node} />
    } else if (node.type === 'grid') {
      return <GridRenderer node={node} />
    } else if (node.type === 'heading1' ||
      node.type === 'heading2' ||
      node.type === 'heading3' ||
      node.type === 'heading4' ||
      node.type === 'heading5' ||
      node.type === 'heading6') {
      return <HeadingRenderer node={node} />
    } else if (node.type === 'view') {
      // view类型节点，使用通用容器渲染器处理子节点
      if (!node.children || node.children.length === 0) {
        return null
      }
      return (
        <ContainerContentRenderer
          children={node.children}
          compact={false}
        />
      )
    } else {
      console.error('不支持的节点类型: ', node)
      // 其他类型的节点渲染
      return <div>不支持的节点类型: {node.type}</div>
    }
  }
}) 