import { DocNode } from '../types'
import { createComponent, required } from '@/pkg/utils/create-component'

type DividerRendererProps = {
  props: {
    node: DocNode
  }
}

export const DividerRenderer = createComponent<DividerRendererProps>({
  props: {
    node: required,
  },
}, props => {
  return () => (
    <div class="my-4">
      <hr class="border-t border-gray-300 dark:border-gray-700" />
    </div>
  )
}) 