import { logger } from '@/pkg/utils/logger'
import { DocNode } from '../types'
import { ref, onMounted, computed } from 'vue'
import { createComponent, required } from '@/pkg/utils/create-component'

type ImageRendererProps = {
  props: {
    node: DocNode
  }
}

export const ImageRenderer = createComponent<ImageRendererProps>({
  props: {
    node: required,
  }
}, props => {
  const { node } = props
  const imageUrl = ref<string | null>(null)
  const loading = ref(true)
  const error = ref(false)
  const originalWidth = ref<number | null>(null)
  const originalHeight = ref<number | null>(null)

  // 计算图片的实际显示尺寸
  const imageStyle = computed(() => {
    const image = node.snapshot?.image
    if (!image) return {}

    const { scale = 1 } = image

    // 如果已知原始尺寸，则按比例设置宽度
    if (originalWidth.value && originalHeight.value) {
      const width = originalWidth.value * scale

      return {
        width: `${width}px`,
        height: 'auto', // 高度自动，保持原始比例
        maxWidth: '100%',
        margin: '0 auto'
      }
    }

    return {
      maxWidth: '100%',
      maxHeight: '100%',
      margin: '0 auto'
    }
  })

  onMounted(() => {
    if (node.snapshot?.image?.token) {
      // 使用token构建高质量图片URL，与md-util.ts中相同的方法
      const token = node.snapshot.image.token
      const highQualityUrl = `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/preview/${token}?preview_type=16`
      // logger.error('highQualityUrl', highQualityUrl)
      // 设置图片URL
      imageUrl.value = highQualityUrl
    } else {
      error.value = true
      loading.value = false
    }
  })

  // 处理图片加载完成
  const handleLoad = (e: Event) => {
    loading.value = false

    // 获取图片的原始尺寸
    const img = e.target as HTMLImageElement
    originalWidth.value = img.naturalWidth
    originalHeight.value = img.naturalHeight
  }

  // 处理图片加载失败
  const handleError = () => {
    error.value = true
    loading.value = false
  }

  return () => (
    <div class="my-4">
      {loading.value && !error.value && (
        <div class="flex justify-center items-center h-64 bg-gray-100 rounded">
          <p class="text-gray-500">图片加载中...</p>
        </div>
      )}

      {error.value && (
        <div class="flex justify-center items-center h-64 bg-gray-100 rounded">
          <p class="text-gray-500">图片加载失败</p>
        </div>
      )}

      {imageUrl.value && (
        <img
          src={imageUrl.value}
          alt={node.snapshot?.image?.name || "图片"}
          class={`${loading.value ? 'hidden' : ''}`}
          onLoad={handleLoad}
          onError={handleError}
          style={imageStyle.value}
        />
      )}

      {node.snapshot?.image?.caption?.text?.initialAttributedTexts?.text?.[0] && (
        <div class="text-center text-gray-500 text-sm mt-2">
          {node.snapshot.image.caption.text.initialAttributedTexts.text[0]}
        </div>
      )}
    </div>
  )
}) 