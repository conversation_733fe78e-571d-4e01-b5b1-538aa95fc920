import { RenderItem, DocNode } from '../types'
import { OrderedListRenderer } from './OrderedListRenderer'
import { BulletListRenderer } from './BulletListRenderer'
import { DocNodeRenderer } from './DocNodeRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type ListGroupRendererProps = {
  props: {
    items: RenderItem[]
    keyPrefix?: string
    level?: number  // 添加层级参数
  }
}

export const ListGroupRenderer = createComponent<ListGroupRendererProps>({
  props: {
    items: required,
    keyPrefix: 'item',
    level: 0,
  },
}, props => {
  const { items, keyPrefix = 'item', level = 0 } = props

  return () => (
    <div>
      {items.map((item, idx) => {
        if ('items' in item && item.type === 'ordered') {
          return (
            <OrderedListRenderer key={`${keyPrefix}-ordered-${idx}`} items={item.items} level={level} />
          );
        } else if ('items' in item && item.type === 'bullet') {
          return (
            <BulletListRenderer key={`${keyPrefix}-bullet-${idx}`} items={item.items} />
          );
        } else {
          // 是DocNode类型
          return (
            <div key={('id' in item) ? item.id : `${keyPrefix}-node-${idx}`}>
              <DocNodeRenderer node={item as DocNode} />
            </div>
          );
        }
      })}
    </div>
  )
}) 