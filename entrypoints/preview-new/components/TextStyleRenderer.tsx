import { InlineComponentRenderer } from './InlineComponentRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type TextStyleRendererProps = {
  props: {
    op: any
    isDone?: boolean
  }
}

export const TextStyleRenderer = createComponent<TextStyleRendererProps>({
  props: {
    op: required,
    isDone: false,
  },
}, props => {
  const { op, isDone = false } = props

  return () => {
    const attrs = op.attributes || {}
    const text = op.insert

    // 优先处理内联组件
    if (attrs['inline-component']) {
      return <InlineComponentRenderer op={op} />
    }

    if (attrs.link) {
      // 处理超链接，需要解码URL
      const decodedLink = decodeURIComponent(attrs.link)
      return <a href={decodedLink} target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">{text}</a>
    } else if (attrs.bold === 'true') {
      return <span class="font-bold">{text}</span>
    } else if (attrs.strikethrough === 'true' || (isDone && attrs['clientside--todo-line-through'] === 'true')) {
      return <span class="line-through">{text}</span>
    } else if (attrs.underline === 'true') {
      return <span class="underline">{text}</span>
    } else if (attrs.inlineCode === 'true') {
      return <span class="font-mono bg-[#f2f3f5] border border-solid border-[#dee0e3] px-1 rounded">{text}</span>
    } else if (attrs.textHighlight) {
      // 处理文本高亮颜色
      const color = attrs.textHighlight
      return <span style={{ color }}>{text}</span>
    } else if (attrs.textHighlightBackground) {
      // 处理背景高亮
      const bgColor = attrs.textHighlightBackground
      return <span style={{ backgroundColor: bgColor }}>{text}</span>
    } else if (isDone) {
      // 如果是已完成的待办事项，添加删除线
      return <span class="line-through">{text}</span>
    } else {
      return <span>{text}</span>
    }
  }
}) 