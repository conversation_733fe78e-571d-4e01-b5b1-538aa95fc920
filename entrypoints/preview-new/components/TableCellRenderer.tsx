import { DocNode } from '../types'
import { ContainerContentRenderer } from './ContainerContentRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type TableCellRendererProps = {
  props: {
    node: DocNode
  }
}

export const TableCellRenderer = createComponent<TableCellRendererProps>({
  props: {
    node: required,
  },
}, props => {
  const { node } = props

  return () => {
    if (!node.children || node.children.length === 0) {
      return <span class="text-gray-400">空单元格</span>
    }

    // 使用通用容器内容渲染器，启用紧凑模式适合表格环境
    return (
      <ContainerContentRenderer
        children={node.children}
        compact={true}
      />
    )
  }
}) 