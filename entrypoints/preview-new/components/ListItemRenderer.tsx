import { DocNode } from '../types'
import { TextStyleRenderer } from './TextStyleRenderer'
import { groupAdjacentLists } from '../utils/list-grouping'
import { ListGroupRenderer } from './ListGroupRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type ListItemRendererProps = {
  props: {
    node: DocNode
    level?: number  // 添加层级参数
  }
}

export const ListItemRenderer = createComponent<ListItemRendererProps>({
  props: {
    node: required,
    level: 0,
  },
}, props => {
  const { node, level = 0 } = props

  return () => {
    if (!node.zoneState || !node.zoneState.content) {
      return null
    }

    const textContent = node.zoneState.content.ops.map((op, idx) => {
      if (op.insert === '\n') return null
      return <span key={idx}><TextStyleRenderer op={op} /></span>
    })

    // 渲染子节点，使用工具函数处理嵌套列表
    const renderChildren = () => {
      if (!node.children || node.children.length === 0) {
        return null
      }

      // 使用工具函数处理列表分组
      const processedChildren = groupAdjacentLists(node.children)

      return (
        <div class="mt-2">
          <ListGroupRenderer items={processedChildren} keyPrefix={`nested-${node.id}`} level={level} />
        </div>
      )
    }

    return (
      <li>
        <div>{textContent}</div>
        {renderChildren()}
      </li>
    )
  }
}) 