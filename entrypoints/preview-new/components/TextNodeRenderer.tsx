import { DocNode } from '../types'
import { TextStyleRenderer } from './TextStyleRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type TextNodeRendererProps = {
  props: {
    node: DocNode
  }
}

export const TextNodeRenderer = createComponent<TextNodeRendererProps>({
  props: {
    node: required,
  },
}, props => {
  const { node } = props

  return () => {
    if (!node.zoneState || !node.zoneState.content) {
      return null
    }

    const textContent = node.zoneState.content.ops.map((op, idx) => {
      if (op.insert === '\n') return <br key={idx} />
      return <span key={idx}><TextStyleRenderer op={op} /></span>
    })

    return (
      <div class="my-1 text-base" style={{ fontSize: '16px' }}>
        {textContent}
      </div>
    )
  }
}) 