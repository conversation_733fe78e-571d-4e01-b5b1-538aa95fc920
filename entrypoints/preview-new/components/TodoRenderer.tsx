import { DocNode } from '../types'
import { TextStyleRenderer } from './TextStyleRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type TodoRendererProps = {
  props: {
    node: DocNode
  }
}

export const TodoRenderer = createComponent<TodoRendererProps>({
  props: {
    node: required,
  },
}, props => {
  const { node } = props

  return () => {
    const isDone = node.snapshot?.done === true

    if (!node.zoneState || !node.zoneState.content) {
      return null
    }

    const textContent = node.zoneState.content.ops.map((op, idx) => {
      if (op.insert === '\n') return <br key={idx} />
      return <span key={idx}><TextStyleRenderer op={op} isDone={isDone} /></span>
    })

    return (
      <div class="flex items-start gap-2 text-base" style={{ fontSize: '16px' }}>
        <input
          type="checkbox"
          checked={isDone}
          disabled
          class="mt-1 h-4 w-4 rounded border-gray-300 text-blue-600"
        />
        <div class={isDone ? "text-gray-500" : ""}>
          {textContent}
        </div>
      </div>
    )
  }
}) 