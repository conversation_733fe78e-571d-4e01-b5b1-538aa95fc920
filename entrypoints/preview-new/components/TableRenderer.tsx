import { DocNode } from '../types'
import { DocNodeRenderer } from './DocNodeRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type TableRendererProps = {
  props: {
    node: DocNode
  }
}

export const TableRenderer = createComponent<TableRendererProps>({
  props: {
    node: required,
  },
}, props => {
  const { node } = props

  return () => {
    if (!node.snapshot || !node.snapshot.rows_id || !node.snapshot.columns_id || !node.children) {
      return <div>表格数据不完整</div>
    }

    const { rows_id, columns_id } = node.snapshot
    const rowCount = rows_id.length
    const colCount = columns_id.length

    // 根据分析，飞书表格数据可能不是按规则的行列排序的
    // 我们直接使用所有 table_cell 子节点
    const tableCells = node.children.filter(child => child.type === 'table_cell')

    // 假设每行的单元格数量等于列数
    // 将单元格分组为行
    const rows: DocNode[][] = []
    let currentRow: DocNode[] = []

    tableCells.forEach((cell, index) => {
      currentRow.push(cell)

      // 当累积了足够的单元格形成一行时，将其添加到行数组中
      if (currentRow.length === colCount) {
        rows.push([...currentRow])
        currentRow = []
      }
    })

    // 如果还有未添加的单元格，添加最后一行
    if (currentRow.length > 0) {
      // 填充剩余的空单元格
      while (currentRow.length < colCount) {
        currentRow.push({
          id: -1,
          type: 'table_cell',
          snapshot: { type: 'table_cell' },
          record: { id: '' },
          zoneState: { allText: '', content: { ops: [] } },
          children: []
        })
      }
      rows.push(currentRow)
    }

    return (
      <div class="my-4 overflow-x-auto">
        <table class="min-w-full border-collapse border border-gray-300">
          <tbody>
            {rows.map((row, rowIndex) => (
              <tr key={rowIndex}>
                {row.map((cell, cellIndex) => (
                  <td key={`${rowIndex}-${cellIndex}`} class="border border-gray-300 p-2">
                    {cell.id !== -1 ? (
                      <DocNodeRenderer node={cell} />
                    ) : (
                      <span class="text-gray-400">空单元格</span>
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    )
  }
}) 