import { DocNode } from '../types'
import { ListItemRenderer } from './ListItemRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type OrderedListRendererProps = {
  props: {
    items: DocNode[]
    level?: number  // 添加层级参数
  }
}

export const OrderedListRenderer = createComponent<OrderedListRendererProps>({
  props: {
    items: required,
    level: 0,
  },
}, props => {
  const { items, level = 0 } = props

  return () => {
    // 根据层级确定列表样式
    const getListStyle = (level: number) => {
      switch (level % 4) {
        case 0: return 'decimal'        // 1, 2, 3...
        case 1: return 'lower-alpha'    // a, b, c...
        case 2: return 'lower-roman'    // i, ii, iii...
        case 3: return 'decimal'        // 1, 2, 3... (重复)
        default: return 'decimal'
      }
    }

    // 根据层级添加适当的缩进
    const getMarginLeft = (level: number) => {
      return level > 0 ? '20px' : '0px'
    }

    return (
      <ol
        style={{
          listStyleType: getListStyle(level),
          marginLeft: getMarginLeft(level),
          paddingLeft: '20px'
        }}
        class="my-2"
      >
        {items.map((node, idx) => (
          <ListItemRenderer key={idx} node={node} level={level + 1} />
        ))}
      </ol>
    )
  }
}) 