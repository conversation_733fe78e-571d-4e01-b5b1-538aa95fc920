import { DocNode } from '../types'
import { ContainerContentRenderer } from './ContainerContentRenderer'
import { createComponent, required } from '@/pkg/utils/create-component'

type CalloutRendererProps = {
  props: {
    node: DocNode
  }
}

export const CalloutRenderer = createComponent<CalloutRendererProps>({
  props: {
    node: required,
  },
}, props => {
  const { node } = props

  return () => {
    if (!node.children) {
      return null
    }

    // 获取 callout 的样式属性
    const backgroundColor = node.snapshot.background_color || '#fde2e2'
    const borderColor = node.snapshot.border_color || '#f98e8b'
    const borderRadius = '8px'

    // 处理 emoji 显示
    let emoji = '📍' // 默认 emoji

    if (node.snapshot.emoji_value) {
      // 如果有 emoji_value，使用 Unicode 渲染
      try {
        const codePoint = parseInt(node.snapshot.emoji_value, 16)
        emoji = String.fromCodePoint(codePoint)
      } catch (e) {
        // 如果解析失败，回退到预定义的 emoji
        emoji = getEmojiFromId(node.snapshot.emoji_id || 'round_pushpin')
      }
    } else if (node.snapshot.emoji_id) {
      // 否则使用 emoji_id
      emoji = getEmojiFromId(node.snapshot.emoji_id)
    }

    return (
      <div
        class="my-4 relative"
        style={{
          backgroundColor,
          borderRadius,
          padding: '16px 16px 16px 48px',
          border: `1px solid ${borderColor}`
        }}
      >
        {/* 位于左侧的图标 */}
        <div
          class="absolute"
          style={{
            top: '20px',
            left: '20px'
          }}
        >
          {emoji}
        </div>

        {/* Callout 内容 */}
        <div>
          <ContainerContentRenderer
            children={node.children}
            compact={false}
          />
        </div>
      </div>
    )
  }
})

// 根据 emoji_id 返回对应的 emoji 字符
function getEmojiFromId(emojiId: string): string {
  const emojiMap: Record<string, string> = {
    'round_pushpin': '📍',
    'glass_of_milk': '🥛',
    // 可以根据需要添加更多 emoji 映射
  }

  return emojiMap[emojiId] || '📍' // 默认返回 round_pushpin
} 