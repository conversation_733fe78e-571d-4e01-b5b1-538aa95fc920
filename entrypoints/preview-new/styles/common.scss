/* 嵌套列表样式 */

/* 有序列表 - 三层循环样式 */
ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  
  ol {
    list-style-type: lower-alpha;
    
    ol {
      list-style-type: lower-roman;
      
      ol {
        list-style-type: decimal;
        
        ol {
          list-style-type: lower-alpha;
          
          ol {
            list-style-type: lower-roman;
            
            ol {
              list-style-type: decimal;
              
              ol {
                list-style-type: lower-alpha;
                
                ol {
                  list-style-type: lower-roman;
                  
                  ol {
                    list-style-type: decimal;
                    
                    ol {
                      list-style-type: lower-alpha;
                      
                      ol {
                        list-style-type: lower-roman;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

/* 无序列表 - 三层循环样式 */
ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  
  ul {
    list-style-type: circle;
    
    ul {
      list-style-type: square;
      
      ul {
        list-style-type: disc;
        
        ul {
          list-style-type: circle;
          
          ul {
            list-style-type: square;
            
            ul {
              list-style-type: disc;
              
              ul {
                list-style-type: circle;
                
                ul {
                  list-style-type: square;
                  
                  ul {
                    list-style-type: disc;
                    
                    ul {
                      list-style-type: circle;
                      
                      ul {
                        list-style-type: square;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

/* 列表项样式 */
li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

/* 确保列表项内容正确对齐 */
ol li, ul li {
  padding-left: 0.25rem;
}

/* 设置所有列表序号和标记的颜色 */
ol li::marker, ul li::marker {
  color: #1456F0;
}
