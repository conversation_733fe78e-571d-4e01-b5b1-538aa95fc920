/* 针对浅色/深色模式的 Shiki 代码高亮样式 */

/* 浅色模式 - 默认 */
.shiki,
.shiki span {
  color: var(--shiki-light);
  background-color: var(--shiki-light-bg);
  font-style: var(--shiki-light-font-style);
  font-weight: var(--shiki-light-font-weight);
  text-decoration: var(--shiki-light-text-decoration);
}

/* 深色模式 - 使用媒体查询 */
@media (prefers-color-scheme: dark) {
  .shiki,
  .shiki span {
    color: var(--shiki-dark) !important;
    background-color: var(--shiki-dark-bg) !important;
    font-style: var(--shiki-dark-font-style) !important;
    font-weight: var(--shiki-dark-font-weight) !important;
    text-decoration: var(--shiki-dark-text-decoration) !important;
  }
}

/* 深色模式 - 使用 class 选择器，适用于手动切换主题的网站 */
.dark .shiki,
.dark .shiki span {
  color: var(--shiki-dark) !important;
  background-color: var(--shiki-dark-bg) !important;
  font-style: var(--shiki-dark-font-style) !important;
  font-weight: var(--shiki-dark-font-weight) !important;
  text-decoration: var(--shiki-dark-text-decoration) !important;
}

/* 确保代码块内容在行内换行时正确显示 */
.shiki {
  overflow-x: auto;
}

.shiki code {
  display: block;
}

/* 去除默认背景颜色，我们将使用容器元素控制背景色 */
.shiki:not(pre) {
  background-color: transparent !important;
}

/* shiki-container 样式 */
.shiki-container {
  margin: 0;
  padding: 0;
  overflow-x: auto;
}

/* 调整 shiki 代码块的样式 */
.shiki-container .shiki {
  margin: 0;
  padding: 1rem;
  border-radius: 0 0 0.375rem 0.375rem;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  background-color: #f5f6f7 !important;
}

/* 行号样式（如果有） */
.shiki-container .line-number {
  user-select: none;
  color: #888;
  margin-right: 1rem;
  display: inline-block;
  text-align: right;
  min-width: 1.5em;
} 