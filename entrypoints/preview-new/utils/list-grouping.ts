import { DocNode, RenderItem, ListGroup } from '../types'

/**
 * 将相邻的列表节点分组为列表组
 * @param children 子节点数组
 * @returns 处理后的渲染项数组，包含分组的列表和普通节点
 */
export function groupAdjacentLists(children: DocNode[]): RenderItem[] {
  const processedChildren: RenderItem[] = [];
  let currentList: ListGroup | null = null;

  for (let i = 0; i < children.length; i++) {
    const child = children[i];

    if (child.type === 'ordered') {
      // 如果当前有一个正在处理的有序列表
      if (currentList && currentList.type === 'ordered') {
        currentList.items.push(child);
      } else {
        // 开始新的有序列表
        if (currentList) {
          processedChildren.push(currentList);
        }
        currentList = { type: 'ordered', items: [child] };
      }
    } else if (child.type === 'bullet') {
      // 如果当前有一个正在处理的无序列表
      if (currentList && currentList.type === 'bullet') {
        currentList.items.push(child);
      } else {
        // 开始新的无序列表
        if (currentList) {
          processedChildren.push(currentList);
        }
        currentList = { type: 'bullet', items: [child] };
      }
    } else {
      // 不是列表类型，结束当前列表处理
      if (currentList) {
        processedChildren.push(currentList);
        currentList = null;
      }
      processedChildren.push(child);
    }
  }

  // 处理最后一个列表
  if (currentList) {
    processedChildren.push(currentList);
  }

  return processedChildren;
} 