 // 1. Import the style
import './style.scss';
import { createApp } from 'vue';
import App from './App.vue';

export default defineContentScript({
  matches: [
    "https://*.feishu.cn/*",
    "https://*.feishu.net/*",
    "https://*.larksuite.com/*",
    "https://*.feishu-pre.net/*",
    "https://*.larkoffice.com/*",
  ],
  runAt: "document_end",
  // 2. Set cssInjectionMode
  cssInjectionMode: 'ui',

  async main(ctx) {
    // 3. Define your UI
    const ui = await createShadowRootUi(ctx, {
      name: 'example-ui',
      position: 'inline',
      anchor: 'body',
      onMount: (container) => {
        // Define how your UI will be mounted inside the container
        const app = createApp(App);
        app.mount(container);
        return app;
      },
      onRemove: (app) => {
        // Unmount the app when the UI is removed
        app?.unmount();
      },
    });

    // 4. Mount the UI
    ui.mount();
  },
});