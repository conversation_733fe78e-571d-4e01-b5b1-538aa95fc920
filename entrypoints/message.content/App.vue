<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'

// 消息类型定义
type MessageType = 'success' | 'error' | 'warning' | 'info' | 'loading'

// Props定义
interface Props {
  message?: string
  type?: MessageType
  duration?: number // 自动隐藏时间，0表示不自动隐藏
  closable?: boolean // 是否显示关闭按钮
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
}

const props = withDefaults(defineProps<Props>(), {
  message: '消息内容',
  type: 'info',
  duration: 3000,
  closable: true,
  position: 'top-center'
})

// 消息状态管理
interface MessageState {
  id: string
  content: string
  type: MessageType
  visible: boolean
  showActions: boolean
  confirmText: string
  cancelText: string
  actionCallback?: () => void
  cancelCallback?: () => void
}

// 组件状态
const messages = ref<Map<string, MessageState>>(new Map())
const autoHideTimers = ref<Map<string, ReturnType<typeof setTimeout>>>(new Map())

// 计算当前显示的消息（只显示一个）
const currentMessage = computed(() => {
  const visibleMessages = Array.from(messages.value.values()).filter(msg => msg.visible)
  return visibleMessages.length > 0 ? visibleMessages[visibleMessages.length - 1] : null
})

// 计算样式类
const messageClass = computed(() => {
  const msg = currentMessage.value
  if (!msg) return []

  return [
    'message-container',
    `message-${msg.type}`,
    `message-${props.position}`,
    msg.showActions ? 'message-with-actions' : ''
  ]
})

// 关闭指定消息
const closeMessage = (messageId: string) => {
  const message = messages.value.get(messageId)
  if (message) {
    message.visible = false

    // 清除自动隐藏定时器
    const timer = autoHideTimers.value.get(messageId)
    if (timer) {
      clearTimeout(timer)
      autoHideTimers.value.delete(messageId)
    }

    // 延迟删除消息
    setTimeout(() => {
      messages.value.delete(messageId)
    }, 300) // 等待动画完成
  }
}

// 关闭当前消息
const closeCurrentMessage = () => {
  const msg = currentMessage.value
  if (msg) {
    closeMessage(msg.id)
  }
}

// 确认操作
const handleConfirm = () => {
  const msg = currentMessage.value
  if (msg) {
    // 执行回调
    msg.actionCallback?.()

    // 发送确认消息到页面
    window.postMessage({
      type: 'MESSAGE_ACTION',
      action: 'confirm',
      messageId: msg.id
    }, '*')

    closeMessage(msg.id)
  }
}

// 取消操作
const handleCancel = () => {
  const msg = currentMessage.value
  if (msg) {
    // 执行回调
    msg.cancelCallback?.()

    // 发送取消消息到页面
    window.postMessage({
      type: 'MESSAGE_ACTION',
      action: 'cancel',
      messageId: msg.id
    }, '*')

    closeMessage(msg.id)
  }
}

// 设置自动隐藏定时器
const setAutoHideTimer = (messageId: string, duration: number) => {
  // 清除之前的定时器
  const existingTimer = autoHideTimers.value.get(messageId)
  if (existingTimer) {
    clearTimeout(existingTimer)
  }

  // 只有在duration大于0时才设置自动隐藏
  if (duration > 0) {
    const timer = setTimeout(() => {
      closeMessage(messageId)
    }, duration)

    autoHideTimers.value.set(messageId, timer)
  }
}

// 消息数据接口
interface MessageData {
  key?: string
  messageId?: string
  message?: string
  messageType?: MessageType
  duration?: number
  showConfirm?: boolean
  confirmButtonText?: string
  cancelButtonText?: string
  keepAlive?: boolean
  closable?: boolean
  actionText?: string
}

// 添加或更新消息
const addOrUpdateMessage = (data: MessageData) => {
  const messageId = data.key || data.messageId || `message_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  // console.log('🔄 addOrUpdateMessage 被调用:', { messageId, data })
  const {
    message,
    messageType = 'info',
    duration = 3000,
    showConfirm = false,
    confirmButtonText = '确认',
    cancelButtonText = '取消',
    keepAlive = false,
    closable = true,
    actionText = ''
  } = data

  // 如果没有指定key，清除所有现有消息
  if (!data.key && !data.messageId) {
    const existingMessages = Array.from(messages.value.keys())
    existingMessages.forEach(id => {
      closeMessage(id)
    })
  }
  // 如果有指定key，不在这里清除，而是在后面直接更新或创建

  const currentMsg = currentMessage.value
  if (currentMsg && currentMsg.id === messageId && currentMsg.showActions) {
    // 如果当前是确认框，且新消息不是强制替换（如再次弹出确认框），则忽略本次普通消息
    if (!showConfirm && !actionText) {
      return
    }
  }

  const existingMessage = messages.value.get(messageId)

  if (existingMessage) {
    // 更新现有消息

    // 清除现有的自动隐藏定时器
    const existingTimer = autoHideTimers.value.get(messageId)
    if (existingTimer) {
      clearTimeout(existingTimer)
      autoHideTimers.value.delete(messageId)
    }

    existingMessage.content = message || existingMessage.content
    existingMessage.type = messageType
    existingMessage.showActions = showConfirm || !!actionText
    existingMessage.confirmText = actionText || confirmButtonText
    existingMessage.cancelText = cancelButtonText
    existingMessage.visible = true
  } else {
    // 创建新消息
    const newMessage: MessageState = {
      id: messageId,
      content: message || props.message,
      type: messageType as MessageType,
      visible: true,
      showActions: showConfirm || !!actionText,
      confirmText: actionText || confirmButtonText,
      cancelText: cancelButtonText
    }

    messages.value.set(messageId, newMessage)
  }

  // 设置自动隐藏（loading类型和keepAlive为true时不自动隐藏）
  if (!keepAlive && messageType !== 'loading') {
    setAutoHideTimer(messageId, duration)
  }
}

// 移除指定消息
const removeMessage = (messageId: string) => {
  closeMessage(messageId)
}

// 监听来自页面的消息
const handleMessage = (event: MessageEvent) => {
  if (event.data && event.data.type === 'FEISHU_EXPORT_MESSAGE') {
    addOrUpdateMessage(event.data)
  } else if (event.data && event.data.type === 'FEISHU_EXPORT_REMOVE_MESSAGE') {
    removeMessage(event.data.key || event.data.messageId)
  }
}

onMounted(() => {
  window.addEventListener('message', handleMessage)
})

// 获取图标
const getIcon = (type: MessageType) => {
  switch (type) {
    case 'success':
      return '✓'
    case 'error':
      return '✕'
    case 'warning':
      return '⚠'
    case 'loading':
      return '⟳'  // loading使用和info相同的图标
    case 'info':
    default:
      return 'ℹ'
  }
}
</script>

<template>
  <Transition name="message" appear>
    <div v-if="currentMessage" :class="messageClass"
      @click="!currentMessage.showActions ? closeCurrentMessage : undefined">
      <div class="message-content">
        <span class="message-icon" :class="{ 'message-loading': currentMessage.type === 'loading' }">
          {{ getIcon(currentMessage.type) }}
        </span>
        <span class="message-text">{{ currentMessage.content }}</span>

        <!-- 操作按钮组 -->
        <div v-if="currentMessage.showActions" class="message-actions" @click.stop>
          <button class="message-button message-confirm" @click="handleConfirm">
            {{ currentMessage.confirmText }}
          </button>
          <button class="message-button message-cancel" @click="handleCancel">
            {{ currentMessage.cancelText }}
          </button>
        </div>

        <!-- 关闭按钮 -->
        <button v-if="props.closable && !currentMessage.showActions" class="message-close"
          @click.stop="closeCurrentMessage" aria-label="关闭">
          ×
        </button>
      </div>
    </div>
  </Transition>
</template>

<style lang="scss" scoped>
.message-container {
  position: fixed;
  z-index: 99999;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  cursor: pointer;
  user-select: none;
  max-width: 480px;
  min-width: 300px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
  }

  // 位置样式
  &.message-top-right {
    top: 20px;
    right: 20px;
  }

  &.message-top-left {
    top: 20px;
    left: 20px;
  }

  &.message-bottom-right {
    bottom: 20px;
    right: 20px;
  }

  &.message-bottom-left {
    bottom: 20px;
    left: 20px;
  }

  &.message-top-center {
    top: 24px;
    left: 50%;
    transform: translateX(-50%);

    &:hover {
      transform: translateX(-50%) translateY(-1px);
    }
  }

  &.message-bottom-center {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);

    &:hover {
      transform: translateX(-50%) translateY(-1px);
    }
  }

  // 消息类型样式 - 简化配色
  &.message-success {
    background: rgba(240, 253, 244, 0.95);
    color: #166534;
    border-left: 3px solid #22c55e;

    &:hover {
      background: rgba(240, 253, 244, 1);
    }
  }

  &.message-error {
    background: rgba(254, 242, 242, 0.95);
    color: #991b1b;
    border-left: 3px solid #ef4444;

    &:hover {
      background: rgba(254, 242, 242, 1);
    }
  }

  &.message-warning {
    background: rgba(254, 252, 232, 0.95);
    color: #92400e;
    border-left: 3px solid #f59e0b;

    &:hover {
      background: rgba(254, 252, 232, 1);
    }
  }

  &.message-info {
    background: rgba(239, 246, 255, 0.95);
    color: #1e40af;
    border-left: 3px solid #3b82f6;

    &:hover {
      background: rgba(239, 246, 255, 1);
    }
  }
}

.message-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.message-icon {
  font-size: 16px;
  font-weight: bold;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;

  // 为不同类型设置图标背景
  .message-success & {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
  }

  .message-error & {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  .message-warning & {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
  }

  .message-info & {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }
}

.message-text {
  flex: 1;
  line-height: 1.5;
  word-break: break-word;
  font-size: 14px;
  font-weight: 500;
}

.message-close {
  background: rgba(0, 0, 0, 0.04);
  border: none;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.08);
    color: #374151;
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
}

// 动画效果
.message-enter-active {
  transition: all 0.3s ease;
}

.message-leave-active {
  transition: all 0.25s ease;
}

.message-enter-from {
  opacity: 0;
  transform: translateX(-50%) translateY(-10px) scale(0.95);
}

.message-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(-5px) scale(0.98);
}

// 确保top-center位置的动画正确
.message-top-center {
  &.message-enter-from {
    transform: translateX(-50%) translateY(-10px) scale(0.95);
  }

  &.message-leave-to {
    transform: translateX(-50%) translateY(-5px) scale(0.98);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .message-container {
    max-width: calc(100vw - 48px);
    padding: 14px 20px;
    border-radius: 6px;

    &.message-top-center {
      left: 50% !important;
      transform: translateX(-50%) !important;
      max-width: calc(100vw - 48px);

      &:hover {
        transform: translateX(-50%) translateY(-1px) !important;
      }

      &.message-enter-from {
        transform: translateX(-50%) translateY(-10px) scale(0.95) !important;
      }

      &.message-leave-to {
        transform: translateX(-50%) translateY(-5px) scale(0.98) !important;
      }
    }
  }

  .message-text {
    font-size: 13px;
  }

  .message-icon {
    width: 18px;
    height: 18px;
    font-size: 14px;
  }

  .message-close {
    width: 18px;
    height: 18px;
    font-size: 12px;
  }
}

// 操作按钮样式
.message-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.message-button {
  padding: 6px 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 50px;

  &:active {
    transform: scale(0.98);
  }
}

.message-confirm {
  background: #f9fafb;
  color: #374151;

  &:hover {
    background: #f3f4f6;
    border-color: rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }
}

.message-cancel {
  background: transparent;
  color: #6b7280;

  &:hover {
    background: rgba(0, 0, 0, 0.04);
    color: #374151;
    transform: translateY(-1px);
  }
}

// 有操作按钮时的容器样式调整
.message-with-actions {
  cursor: default;

  .message-content {
    gap: 16px;
  }

  .message-text {
    margin-right: 8px;
  }
}

// 响应式操作按钮
@media (max-width: 768px) {
  .message-actions {
    gap: 6px;
  }

  .message-button {
    padding: 5px 10px;
    font-size: 11px;
    min-width: 45px;
  }
}

// loading图标样式（无特殊动画）
.message-loading {
  // 保持和其他图标一致的样式
}

// loading类型使用和info相同的样式
.message-container.message-loading {
  background: rgba(239, 246, 255, 0.95);
  color: #1e40af;
  border-left: 3px solid #3b82f6;

  &:hover {
    background: rgba(239, 246, 255, 1);
  }
}
</style>