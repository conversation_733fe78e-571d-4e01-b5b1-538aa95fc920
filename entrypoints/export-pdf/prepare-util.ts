import { generatePublicUrl, makePublicUrlEffective } from "@/pkg/lark/image"
import { isDefined, OneHundred, Second, waitFor } from "@/pkg/common"
import { docx } from "@/pkg/lark/docx"
import { Toast } from "@/pkg/lark/env"
import normalizeFileName from 'filenamify/browser'
import {
  processImageParallel,
  processImageParallelOriginal,
  IMAGE_CONFIGS,
  DEFAULT_IMAGE_CONFIG
} from "../utils/common"

export interface PrepareResult {
  isReady: boolean
  recoverScrollTop?: () => void
}

export interface ProcessedData {
  root: any
  images: any[]
  recommendName: string
  recoverScrollTop?: () => void
}

export interface PrepareOptions {
  exportType?: 'pdf' | 'image' | 'word'
  optimizeImages?: boolean
}

// 基础文档准备函数（私有）
const prepareDocument = async (): Promise<PrepareResult> => {
  const checkIsReady = () => docx.isReady({ checkWhiteboard: true })

  let recoverScrollTop

  if (!checkIsReady()) {
    const initialScrollTop = docx.container?.scrollTop ?? 0
    recoverScrollTop = () => {
      docx.scrollTo({
        top: initialScrollTop,
        behavior: 'instant',
      })
    }

    let top = 0

    docx.scrollTo({
      top,
      behavior: 'instant',
    })

    const maxTryTimes = OneHundred
    let tryTimes = 0

    Toast.loading({
      content: '正在加载文档内容...',
      keepAlive: true,
      key: 'pdf-export',
      actionText: '取消',
      onActionClick: () => {
        tryTimes = maxTryTimes
      },
    })

    while (!checkIsReady() && tryTimes <= maxTryTimes) {
      docx.scrollTo({
        top,
        behavior: 'smooth',
      })

      await waitFor(0.4 * Second)

      tryTimes++

      top = docx.container?.scrollHeight ?? 0
    }

    Toast.remove('pdf-export')
  }

  return {
    isReady: checkIsReady(),
    recoverScrollTop,
  }
}

// PDF导出专用的数据准备函数（优化版）
export const prepareExportDataForPdf = async (): Promise<{ recommendName: string, recoverScrollTop?: () => void } | null> => {
  if (!docx.rootBlock) {
    Toast.error({ content: '❌ 不支持的文档类型' })
    return null
  }

  // 先准备文档，确保内容加载完成
  const { isReady, recoverScrollTop } = await prepareDocument()
  if (!isReady) {
    Toast.error({ content: '❌ 文档加载失败，请刷新页面重试' })
    return null
  }

  // PDF导出只需要文档标题，不需要转换为Markdown AST
  const recommendName = docx.pageTitle
    ? normalizeFileName(docx.pageTitle.slice(0, OneHundred))
    : 'doc'

  return {
    recommendName,
    recoverScrollTop
  }
}

// 统一的数据准备函数（保持向后兼容）
export const prepareExportData = async (options: PrepareOptions = {}): Promise<ProcessedData | null> => {
  const { exportType = 'pdf', optimizeImages = true } = options

  if (!docx.rootBlock) {
    Toast.error({ content: '❌ 不支持的文档类型' })
    return null
  }
  // 先准备所有数据，避免在用户确认后进行太多异步操作
  const { isReady, recoverScrollTop } = await prepareDocument()
  if (!isReady) {
    Toast.error({ content: '❌ 文档加载失败，请刷新页面重试' })
    return null
  }

  Toast.loading({ content: '正在分析文档结构...', keepAlive: true, key: "pdf-export" })

  const { root, images } = docx.intoMarkdownAST()


  const recommendName = docx.pageTitle
    ? normalizeFileName(docx.pageTitle.slice(0, OneHundred))
    : 'doc'

  // 处理图片URL和token
  const tokens = images
    .map(image => {
      if (!image.data?.token) return null
      const { token } = image.data
      const publicUrl = generatePublicUrl(token)
      const code = new URL(publicUrl).searchParams.get('code')
      if (!code) return null

      // 设置图片URL
      image.url = publicUrl
      return [token, code]
    })
    .filter(isDefined)

  // 先确保所有图片URL生效
  if (tokens.length > 0) {
    const isSuccess = await makePublicUrlEffective(
      Object.fromEntries(tokens) as Record<string, string>,
    )
    if (!isSuccess) {
      // 把图片转换为dataurl
      if (optimizeImages) {
        console.warn('图片公开URL失效，开始并行转换和优化为dataURL')

        // 获取当前导出类型的优化配置
        const optimizationConfig = IMAGE_CONFIGS[exportType as keyof typeof IMAGE_CONFIGS] || DEFAULT_IMAGE_CONFIG

        // 更新进度提示
        Toast.loading({
          content: `正在处理图片 (0/${images.length})...`,
          keepAlive: true,
          key: "pdf-export"
        })

        // 智能并发处理所有图片
        const concurrency = Math.min(8, Math.max(2, navigator.hardwareConcurrency || 4)) // 根据CPU核心数动态调整，最少2个最多8个
        let processedCount = 0
        // 创建一个并发控制的处理函数
        const processWithConcurrency = async () => {
          const promises = []

          for (let i = 0; i < images.length; i++) {
            const promise = processImageParallel(images[i], i, images.length, optimizationConfig).then(() => {
              processedCount++
              // 更新进度
              Toast.loading({
                content: `正在处理图片 (${processedCount}/${images.length})...`,
                keepAlive: true,
                key: "pdf-export"
              })
            })

            promises.push(promise)

            // 当达到并发限制时，等待一些任务完成
            if (promises.length >= concurrency) {
              await Promise.race(promises)
              // 移除已完成的promise
              for (let j = promises.length - 1; j >= 0; j--) {
                if (await Promise.race([promises[j], Promise.resolve('pending')]) !== 'pending') {
                  promises.splice(j, 1)
                }
              }
            }
          }

          // 等待所有剩余任务完成
          await Promise.all(promises)
        }

        await processWithConcurrency()

      } else {
        console.warn('图片公开URL失效，开始转换为dataURL（不进行优化）')

        // 更新进度提示
        Toast.loading({
          content: `正在处理图片 (0/${images.length})...`,
          keepAlive: true,
          key: "pdf-export"
        })

        // 智能并发处理所有图片（不优化版本）
        const concurrency = Math.min(8, Math.max(2, navigator.hardwareConcurrency || 4)) // 根据CPU核心数动态调整，最少2个最多8个
        let processedCount = 0

        // 创建一个并发控制的处理函数
        const processWithConcurrency = async () => {
          const promises = []

          for (let i = 0; i < images.length; i++) {
            const promise = processImageParallelOriginal(images[i], i, images.length).then(() => {
              processedCount++
              // 更新进度
              Toast.loading({
                content: `正在处理图片 (${processedCount}/${images.length})...`,
                keepAlive: true,
                key: "pdf-export"
              })
            })

            promises.push(promise)

            // 当达到并发限制时，等待一些任务完成
            if (promises.length >= concurrency) {
              await Promise.race(promises)
              // 移除已完成的promise
              for (let j = promises.length - 1; j >= 0; j--) {
                if (await Promise.race([promises[j], Promise.resolve('pending')]) !== 'pending') {
                  promises.splice(j, 1)
                }
              }
            }
          }

          // 等待所有剩余任务完成
          await Promise.all(promises)
        }

        await processWithConcurrency()

      }
    }
  }

  // 显示完成提示
  Toast.loading({
    content: '✅ 数据准备完成',
    keepAlive: true,
    key: "pdf-export"
  })

  // 短暂显示完成状态
  await new Promise(resolve => setTimeout(resolve, 500))

  Toast.remove('pdf-export')

  return {
    root,
    images,
    recommendName,
    recoverScrollTop
  }
}

