import { printTOC } from "../utils/toc-tree";

import { doInjectScript } from "@/pkg/utils/inject-help";

/**
 * 按照目录顺序批量导出PDF并打包成zip
 */
export async function exportPdfAllInjected(): Promise<void> {
  console.error("开始按目录顺序批量导出PDF");

  try {
    // 查找目录元素
    const tocElement = document.getElementById("TOC-ROOT");
    if (!tocElement) {
      console.error("未找到TOC元素");
      return;
    }

    console.error("找到 TOC 元素");
    // 打印目录树
    await printTOC();

    // 获取所有的 div 元素
    const divs = tocElement.querySelectorAll('div[role="item"]');
    // 过滤出不包含 button 的 div
    const filteredDivs = Array.from(divs).filter((div) => {
      return !div.querySelector("button");
    });

    // 存储所有有文本的按钮
    const buttonsWithText: { button: Element; text: string }[] = [];

    // 遍历所有符合条件的 div，找出所有的 button 元素
    filteredDivs.forEach((div) => {
      const buttons = div.querySelectorAll('[role="button"]');
      if (buttons.length > 0) {
        buttons.forEach((button) => {
          const buttonText = (button as HTMLElement).textContent?.trim();
          if (buttonText) {
            buttonsWithText.push({ button, text: buttonText });
            console.error(`找到目录按钮：${buttonText}`);
          }
        });
      }
    });
    // 如果找到按钮，按顺序点击并导出PDF
    if (buttonsWithText.length > 0) {
      console.error(`找到 ${buttonsWithText.length} 个目录项，开始批量导出...`);

      // 按顺序处理每个按钮，从第一个开始
      for (let i = 0; i < buttonsWithText.length; i++) {
        const button = buttonsWithText[i];

        // 点击按钮前，确保所有菜单都是展开的
        // await printTOC(); // 移除重复的目录打印

        // // 点击按钮，切换到对应内容
        // (button.button as HTMLElement).click();

        // // 等待内容加载
        // await new Promise((resolve) => setTimeout(resolve, 2000));

        // try {
        //   // await doInjectScript();
        //   console.error("点击按钮", button.text);
        // } catch (error) {
        //   console.error(`处理页面 "${button.text}" 时出错:`, error);
        // }
      }

      // 生成zip文件
    } else {
      console.error("没有找到任何目录项，将只导出当前页面");
    }
  } catch (error) {
    console.error("批量导出过程中出错:", error);
  }
}
