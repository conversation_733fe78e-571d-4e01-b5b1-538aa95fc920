import { docx } from "@/pkg/lark/docx";
import { convertDocxToHtmlForPdf } from "@/pkg/lark/html-converter-fn";
import { Toast } from "@/pkg/lark/env";
import { exportOriginalPDF } from "./origin-pdf-export";
import { prepareExportDataForPdf } from "./prepare-util";

export async function exportPdfInjected(action: string) {
  // 获取head里面的name="title" 的meta 标签的content属性
  const title = document
    .querySelector('meta[name="title"]')
    ?.getAttribute("content");
  const title2 = document.querySelector("title")?.textContent;
  const isPdfFile1 = title && title.endsWith(".pdf");
  const isPdfFile2 = title2 && title2.includes(".pdf");
  if (isPdfFile1 || isPdfFile2) {
    console.error("导出原pdf文档");
    await exportOriginalPDF();
    return;
  }

  // 使用PDF专用的优化数据准备逻辑（不需要Markdown AST转换和图片URL生成）
  const data = await prepareExportDataForPdf();
  if (!data) return;

  const { recommendName, recoverScrollTop } = data;

  Toast.loading({
    content: '正在准备PDF数据...',
    key: 'pdf-export'
  });

  // 使用新的HTML转换器直接转换，等待图片处理完成
  const htmlResult = await convertDocxToHtmlForPdf(docx.rootBlock, {
    useInlineStyles: true, // PDF导出使用内联样式，避免样式丢失
    cssClassPrefix: "feishu", // 统一CSS类名前缀
    convertImages: true, // 转换图片
    convertFiles: true, // 转换文件链接
    generateToc: true, // 生成目录
    tocTitle: "目录", // 目录标题
    tocMaxLevel: 6, // 目录最大层级
  });

  // 构建发送给后端的完整HTML结构，包含840px容器
  const htmlForBackend = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${recommendName}</title>
  <style>
    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0;
      color: #2c3e50;
      font-size: 16px;
      background: white;
    }

    .document-container {
      width: 840px;
      margin: 0 auto;
      background: white;
      padding: 40px 20px;
    }

    .document-header {
      text-align: center;
      border-bottom: 1px solid #e0e0e0;
      padding-bottom: 30px;
      margin-bottom: 40px;
    }

    .document-title {
      font-size: 36px;
      font-weight: 700;
      color: #2c3e50;
      margin: 0 0 12px 0;
      line-height: 1.3;
    }

    .document-subtitle {
      font-size: 16px;
      color: #7f8c8d;
      margin: 0;
      font-weight: 400;
    }

    .document-content {
      max-width: 840px;
      margin: 0 auto;
      font-size: 16px;
      line-height: 1.7;
      color: #2c3e50;
    }

    /* 文档内容样式优化 - 与预览页面保持一致 */
    .document-content img {
      max-width: 100% !important;
      height: auto !important;
      display: block !important;
      margin: 20px auto !important;
    }

    .document-content .consecutive-images {
      display: flex !important;
      flex-wrap: nowrap !important;
      align-items: flex-start !important;
      gap: 15px !important;
      margin: 25px 0 !important;
      overflow: hidden !important;
    }

    .document-content .consecutive-images img {
      flex: 1 1 auto !important;
      min-width: 0 !important;
      max-width: none !important;
      width: auto !important;
      height: auto !important;
      object-fit: contain !important;
      margin: 0 !important;
      display: block !important;
    }

    .document-content h1,
    .document-content h2,
    .document-content h3,
    .document-content h4,
    .document-content h5,
    .document-content h6 {
      margin-top: 40px;
      margin-bottom: 20px;
      line-height: 1.4;
      color: #2c3e50;
      font-weight: 600;
    }

    .document-content h1 {
      font-size: 32px;
      padding-bottom: 10px;
    }

    .document-content h2 {
      font-size: 28px;
    }

    .document-content h3 {
      font-size: 24px;
    }

    .document-content p {
      margin: 18px 0;
      text-align: justify;
    }

    .document-content hr {
      margin: 30px 0;
      border: none;
      border-top: 1px solid #e0e0e0;
      height: 1px;
    }

    .document-content table {
      margin: 25px 0;
      border-collapse: collapse;
      width: 100%;
      border: 1px solid #e0e0e0;
    }

    .document-content table th,
    .document-content table td {
      border: 1px solid #e0e0e0;
      padding: 12px 15px;
      text-align: left;
    }

    .document-content table th {
      background: #f8f9fa;
      color: #2c3e50;
      font-weight: 600;
    }

    .document-content table tr:nth-child(even) {
      background-color: #f8f9fa;
    }

    .document-content blockquote {
      margin: 0 !important;
      border-left: 2px solid #bcbfc4;
      color: #646a73;
      padding-left: 10px;
    }

    .document-content p {
      margin: 0 !important;
      padding: 0 !important;
    }

    .document-content a {
      color: #336df4;
      text-decoration: none;
      font-weight: 400;
    }

    .document-content a:hover {
      text-decoration: underline;
      text-underline-offset: 4px;
    }

    .document-content u {
      text-decoration: underline;
      text-underline-offset: 4px;
    }

    .document-content code {
      background: #f4f6f8;
      padding: 4px 8px;
      font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
      font-size: 14px;
      color: #e74c3c;
    }

    .document-content pre {
      background: #f4f6f8;
      color: #2c3e50;
      padding: 20px;
      border: 1px solid #e0e0e0;
      overflow-x: auto;
      margin: 25px 0;
    }

    .document-content pre code {
      background: none;
      padding: 0;
      color: #2c3e50;
    }

    /* 空div样式 - 显示为空行 */
    .document-content div:empty {
      height: 1.7em; /* 与line-height保持一致 */
      display: block;
      margin: 0;
      padding: 0;
    }

    /* 空p标签样式 - 显示为空行 */
    .document-content p:empty {
      height: 1.7em;
      display: block;
      margin: 0;
      padding: 0;
    }

    /* 空blockquote样式 - 显示为空行 */
    .document-content blockquote:empty {
      height: 1.7em;
      display: block;
      margin: 0;
      padding: 0;
      border-left: none; /* 移除左边框，因为是空行 */
    }

    /* 空li样式 - 显示为空行 */
    .document-content li:empty {
      height: 1.7em;
      display: list-item;
      margin: 0;
      padding: 0;
    }

    /* 列表样式 */
    .document-content ul,
    .document-content ol {
      margin: 18px 0;
      padding-left: 30px;
    }

    .document-content li {
      margin: 8px 0;
      line-height: 1.6;
    }

    /* 强调样式 */
    .document-content strong {
      font-weight: 600;
    }

    .document-content em {
      font-style: italic;
    }

    /* 删除线样式 */
    .document-content del {
      text-decoration: line-through;
    }

    @media print {
      body {
        background: white !important;
        color: black !important;
      }

      .document-container {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      .document-header {
        max-width: none !important;
        margin: 0 0 30px 0 !important;
        border-bottom: 1px solid #ccc !important;
        padding-bottom: 20px !important;
      }

      .document-title {
        color: black !important;
      }

      .document-subtitle {
        color: #666 !important;
      }

      .document-content {
        max-width: none !important;
        margin: 0 !important;
        color: black !important;
      }

      /* 确保空行在打印时也能正确显示 */
      .document-content div:empty,
      .document-content p:empty {
        height: 1.7em !important;
        display: block !important;
      }

      .document-content blockquote:empty {
        height: 1.7em !important;
        display: block !important;
        border-left: none !important;
      }

      .document-content li:empty {
        height: 1.7em !important;
        display: list-item !important;
      }
    }
  </style>
</head>
<body>
  <div class="document-container">
    <div class="document-header">
      <h1 class="document-title">${recommendName}</h1>
     </div>
    <div class="document-content">
      ${htmlResult.htmlWithToc}
    </div>
  </div>
</body>
</html>`;

  const tempDiv = document.createElement("div");
  // 使用带目录的HTML，如果目录存在的话
  tempDiv.innerHTML = htmlResult.htmlWithToc || htmlResult.html;

  try {
    window.postMessage(
      {
        action: action,
        data: htmlForBackend, // 发送完整的HTML结构给后端
        title: recommendName,
        originalUrl: window.location.href,
      },
      "*"
    );
  } catch (error) {
    Toast.error({ content: "❌ 打开预览页面失败，请重试" });
  } finally {
    recoverScrollTop?.();
    // 移除loading消息并显示成功消息
    Toast.remove('pdf-export');
    Toast.success({
      content: '请在新窗口打开预览页面下载PDF',
      duration: 5000
    });
  }
}
