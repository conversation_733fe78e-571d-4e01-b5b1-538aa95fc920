import { docx } from "@/pkg/lark/docx";
import { convertDocxToHtmlForPdf } from "@/pkg/lark/html-converter-fn";
import { prepareExportDataForPdf } from "../export-pdf/prepare-util";
import { fileSave, supported } from 'browser-fs-access'
import { Toast } from "@/pkg/lark/env";

export async function exportHtmlInjected() {
  // 显示开始导出消息
  Toast.info({
    content: '开始导出HTML文档...',
    duration: 0, // 不自动隐藏，等待后续状态更新
    key: 'html-export' // 添加固定的key，确保消息能被正确替换
  })

  const data = await prepareExportDataForPdf();
  if (!data) {
    Toast.error({
      content: '数据准备失败，请重试',
      duration: 5000
    })
    return;
  }

  const { recommendName, recoverScrollTop } = data;

  // 生成带时间戳的文件名
  const now = new Date()
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const hour = now.getHours().toString().padStart(2, '0')
  const minute = now.getMinutes().toString().padStart(2, '0')
  const second = now.getSeconds().toString().padStart(2, '0')
  const timestamp = `${year}${month}${day}_${hour}${minute}${second}`
  const filename = `${recommendName}_${timestamp}.html`

  const toBlobContent = async (): Promise<Blob | null> => {
    try {
      // 使用新的HTML转换器直接转换，等待图片处理完成
      const htmlResult = await convertDocxToHtmlForPdf(docx.rootBlock, {
        useInlineStyles: true, // HTML导出使用内联样式，避免样式丢失
        cssClassPrefix: "feishu", // 统一CSS类名前缀
        convertImages: true, // 转换图片
        convertFiles: true, // 转换文件链接
        generateToc: true, // 生成目录
        tocTitle: "目录", // 目录标题
        tocMaxLevel: 6, // 目录最大层级
      });

      // 构建完整的HTML文档结构（参考PDF预览页面的样式）
      const fullHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${recommendName}</title>
  <style>
    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0;
      color: #2c3e50;
      font-size: 16px;
      background: white;
      min-height: 100vh;
    }

    .document-section {
      background: white;
       min-height: 100vh;
    }

    .document-header {
      max-width: 840px;
      margin: 0 auto 40px auto;
      text-align: center;
      border-bottom: 1px solid #e0e0e0;
      padding-bottom: 30px;
    }

    .document-title {
      font-size: 36px;
      font-weight: 700;
      color: #2c3e50;
      margin: 0 0 12px 0;
      line-height: 1.3;
    }

    .document-subtitle {
      font-size: 16px;
      color: #7f8c8d;
      margin: 0;
      font-weight: 400;
    }

    .document-content {
      max-width: 840px;
      margin: 0 auto;
      font-size: 16px;
      line-height: 1.7;
      color: #2c3e50;
    }

    /* 文档内容样式优化 */
    .document-content img {
      max-width: 100% !important;
      height: auto !important;
      display: block !important;
      margin: 20px auto !important;
    }

    .document-content .consecutive-images {
      display: flex !important;
      flex-wrap: nowrap !important;
      align-items: flex-start !important;
      gap: 15px !important;
      margin: 25px 0 !important;
      overflow: hidden !important;
    }

    .document-content .consecutive-images img {
      flex: 1 1 auto !important;
      min-width: 0 !important;
      max-width: none !important;
      width: auto !important;
      height: auto !important;
      object-fit: contain !important;
      margin: 0 !important;
      display: block !important;
    }

    .document-content h1,
    .document-content h2,
    .document-content h3,
    .document-content h4,
    .document-content h5,
    .document-content h6 {
      margin-top: 40px;
      margin-bottom: 20px;
      line-height: 1.4;
      color: #2c3e50;
      font-weight: 600;
    }

    .document-content h1 {
      font-size: 32px;
      padding-bottom: 10px;
    }

    .document-content h2 {
      font-size: 28px;
    }

    .document-content h3 {
      font-size: 24px;
    }

    .document-content p {
      margin: 18px 0;
      text-align: justify;
    }

    .document-content hr {
      margin: 30px 0;
      border: none;
      border-top: 1px solid #e0e0e0;
      height: 1px;
    }

    .document-content table {
      margin: 25px 0;
      border-collapse: collapse;
      width: 100%;
      border: 1px solid #e0e0e0;
    }

    .document-content table th,
    .document-content table td {
      border: 1px solid #e0e0e0;
      padding: 12px 15px;
      text-align: left;
    }

    .document-content table th {
      background: #f8f9fa;
      color: #2c3e50;
      font-weight: 600;
    }

    .document-content table tr:nth-child(even) {
      background-color: #f8f9fa;
    }

    .document-content blockquote {
      margin: 0 !important;
      border-left: 2px solid #bcbfc4;
      color: #646a73;
      padding-left: 10px;
    }

    p {
      margin: 0 !important;
      padding: 0 !important;
    }

    a {
      color: #336df4;
      text-decoration: none;
      font-weight: 400;
    }

    a:hover {
      text-decoration: underline;
      text-underline-offset: 4px;
    }

    u {
      text-decoration: underline;
      text-underline-offset: 4px;
    }

    .document-content code {
      background: #f4f6f8;
      padding: 4px 8px;
      font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
      font-size: 14px;
      color: #e74c3c;
    }

    .document-content pre {
      background: #f4f6f8;
      color: #2c3e50;
      padding: 20px;
      border: 1px solid #e0e0e0;
      overflow-x: auto;
      margin: 25px 0;
    }

    .document-content pre code {
      background: none;
      padding: 0;
      color: #2c3e50;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .document-section {
        padding: 20px;
      }
    }

    /* 打印样式 - 优化打印效果 */
    @media print {
      body {
        background: white !important;
        color: black !important;
      }

      .document-section {
        background: white !important;
        padding: 0 !important;
        box-shadow: none !important;
        border: none !important;
        margin: 0 !important;
      }

      .document-header {
        max-width: none !important;
        margin: 0 0 30px 0 !important;
        border-bottom: 1px solid #ccc !important;
        padding-bottom: 20px !important;
      }

      .document-title {
        color: black !important;
      }

      .document-subtitle {
        color: #666 !important;
      }

      .document-content {
        max-width: none !important;
        margin: 0 !important;
        color: black !important;
      }
    }
  </style>
</head>
<body>
  <div class="document-section">
    <div class="document-header">
      <h1 class="document-title">${recommendName}</h1>
      <div class="document-subtitle">
        导出时间：${new Date().toLocaleString('zh-CN')}
      </div>
    </div>
    
    <div class="document-content">
      ${htmlResult.htmlWithToc || htmlResult.html}
    </div>
  </div>
</body>
</html>`;

      // 创建HTML Blob (不指定charset，避免fileSave API错误)
      const blob = new Blob([fullHtml], { type: 'text/html' });
      return blob;
    } catch (error) {
      console.error('❌ HTML转换失败:', error)
      Toast.error({
        content: 'HTML文档生成失败，请重试',
        duration: 5000
      })
      recoverScrollTop?.()
      return null
    }
  }

  if (!supported) {
    Toast.error({
      content: '当前浏览器不支持文件保存功能，请使用现代浏览器',
      duration: 5000
    })
    recoverScrollTop?.()
    return
  }

  try {
    // 先生成所有内容
    const blob = await toBlobContent()

    // 检查blob是否成功生成
    if (!blob) {
      Toast.error({
        content: 'HTML文档生成失败，请重试',
        duration: 5000
      })
      recoverScrollTop?.()
      return
    }

    // 显示文件大小信息
    const fileSizeMB = (blob.size / 1024 / 1024).toFixed(2)
    console.error(`📄 HTML文件大小: ${fileSizeMB}MB`)

    // 检查用户激活状态，如果失效则显示确认对话框
    if (!navigator.userActivation?.isActive) {
      // 用户激活状态失效时，使用原生confirm
      const confirmed = window.confirm(`HTML文档已准备完成 (${fileSizeMB}MB)，是否立即保存？`)
      
      if (!confirmed) {
        Toast.info({
          content: '导出已取消',
          duration: 3000,
          key: 'html-export'
        })
        recoverScrollTop?.()
        return
      }
    } else {
      // 用户激活状态正常，显示即将保存的提示
      Toast.info({
        content: `HTML文档准备完成 (${fileSizeMB}MB)，即将保存文件...`,
        duration: 2000
      })
    }

    // 大文件处理：添加保存进度提示
    if (blob.size > 5 * 1024 * 1024) { // 大于5MB显示特殊提示
      Toast.info({
        content: `正在保存大文件 (${fileSizeMB}MB)，请耐心等待...`,
        duration: 0,
        key: 'html-export'
      })
    }

    // 使用Promise.race添加超时处理
    const savePromise = fileSave(blob, {
      fileName: filename,
      extensions: ['.html'],
    })

    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('文件保存超时，请重试')), 60000) // 60秒超时
    })

    await Promise.race([savePromise, timeoutPromise])

    // 先显示成功消息，再执行滚动恢复
    console.error('🎉 发送HTML导出成功消息...')
    Toast.success({
      content: 'HTML文档导出成功！',
      duration: 5000,
      key: 'html-export'
    })

    // 延迟执行滚动恢复，避免影响消息显示
    setTimeout(() => {
      recoverScrollTop?.()
    }, 500)

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'

    if (errorMessage.includes('user gesture') || errorMessage.includes('User activation')) {
      Toast.warning({
        content: '文件保存需要用户操作，请重新点击导出按钮',
        duration: 5000
      })
    } else {
      Toast.error({
        content: `文件保存失败: ${errorMessage}`,
        duration: 5000
      })
    }

    recoverScrollTop?.()
  }
}
