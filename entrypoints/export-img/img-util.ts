import { docx } from "@/pkg/lark/docx";
import { convertDocxToHtmlForPdf } from "@/pkg/lark/html-converter-fn";
import { prepareExportDataForPdf } from "../export-pdf/prepare-util";
import { fileSave, supported } from 'browser-fs-access'
import { snapdom } from "@zumer/snapdom";

export const exportImgInjected = async (format: 'png' | 'jpg' = 'png') => {
  // 显示开始导出消息
  window.postMessage({
    type: 'FEISHU_EXPORT_MESSAGE',
    message: `开始导出${format.toUpperCase()}图片...`,
    messageType: 'info',
    duration: 0, // 不自动隐藏，等待后续状态更新
    key: 'img-export'
  }, '*')

  const data = await prepareExportDataForPdf();
  if (!data) {
    window.postMessage({
      type: 'FEISHU_EXPORT_MESSAGE',
      message: '数据准备失败，请重试',
      messageType: 'error',
      duration: 5000
    }, '*')
    return;
  }

  const { recommendName, recoverScrollTop } = data;

  // 生成带时间戳的文件名
  const now = new Date()
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const hour = now.getHours().toString().padStart(2, '0')
  const minute = now.getMinutes().toString().padStart(2, '0')
  const second = now.getSeconds().toString().padStart(2, '0')
  const timestamp = `${year}${month}${day}_${hour}${minute}${second}`
  const extension = format === 'jpg' ? '.jpg' : '.png'
  const filename = `${recommendName}_${timestamp}${extension}`

  const toBlobContent = async (): Promise<Blob | null> => {
    try {
      // 使用新的HTML转换器直接转换，等待图片处理完成
      const htmlResult = await convertDocxToHtmlForPdf(docx.rootBlock, {
        useInlineStyles: true, // 图片导出使用内联样式，避免样式丢失
        cssClassPrefix: "feishu", // 统一CSS类名前缀
        convertImages: true, // 转换图片
        convertFiles: true, // 转换文件链接
        generateToc: true, // 生成目录
        tocTitle: "目录", // 目录标题
        tocMaxLevel: 6, // 目录最大层级
      });

      // 构建完整的HTML文档结构，用于截图
      const fullHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${recommendName}</title>
  <style>
    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0;
      color: #2c3e50;
      font-size: 16px;
      background: white;
      min-height: 100vh;
    }

    .document-section {
      background: white;
      padding: 40px;
      min-height: 100vh;
    }

    .document-header {
      max-width: 840px;
      margin: 0 auto 40px auto;
      text-align: center;
      border-bottom: 1px solid #e0e0e0;
      padding-bottom: 30px;
    }

    .document-title {
      font-size: 36px;
      font-weight: 700;
      color: #2c3e50;
      margin: 0 0 12px 0;
      line-height: 1.3;
    }

    .document-subtitle {
      font-size: 16px;
      color: #7f8c8d;
      margin: 0;
      font-weight: 400;
    }

    .document-content {
      max-width: 840px;
      margin: 0 auto;
      font-size: 16px;
      line-height: 1.7;
      color: #2c3e50;
    }

    .document-content img {
      max-width: 100% !important;
      height: auto !important;
      display: block !important;
      margin: 20px auto !important;
    }

    .document-content h1,
    .document-content h2,
    .document-content h3,
    .document-content h4,
    .document-content h5,
    .document-content h6 {
      margin-top: 40px;
      margin-bottom: 20px;
      line-height: 1.4;
      color: #2c3e50;
      font-weight: 600;
    }

    .document-content h1 {
      font-size: 32px;
      padding-bottom: 10px;
    }

    .document-content h2 {
      font-size: 28px;
    }

    .document-content h3 {
      font-size: 24px;
    }

    .document-content p {
      margin: 18px 0;
      text-align: justify;
    }

    .document-content table {
      margin: 25px 0;
      border-collapse: collapse;
      width: 100%;
      border: 1px solid #e0e0e0;
    }

    .document-content table th,
    .document-content table td {
      border: 1px solid #e0e0e0;
      padding: 12px 15px;
      text-align: left;
    }

    .document-content table th {
      background: #f8f9fa;
      color: #2c3e50;
      font-weight: 600;
    }

    .document-content blockquote {
      margin: 0 !important;
      border-left: 2px solid #bcbfc4;
      color: #646a73;
      padding-left: 10px;
    }

    .document-content code {
      background: #f4f6f8;
      padding: 4px 8px;
      font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
      font-size: 14px;
      color: #e74c3c;
    }

    .document-content pre {
      background: #f4f6f8;
      color: #2c3e50;
      padding: 20px;
      border: 1px solid #e0e0e0;
      overflow-x: auto;
      margin: 25px 0;
    }
  </style>
</head>
<body>
  <div class="document-section">
    <div class="document-header">
      <h1 class="document-title">${recommendName}</h1>
      <div class="document-subtitle">
        导出时间：${new Date().toLocaleString('zh-CN')}
      </div>
    </div>
    
    <div class="document-content">
      ${htmlResult.htmlWithToc || htmlResult.html}
    </div>
  </div>
</body>
</html>`;

      // 创建一个临时的iframe来渲染HTML内容
      const iframe = document.createElement('iframe');
      iframe.style.position = 'fixed';
      iframe.style.left = '-9999px';
      iframe.style.top = '-9999px';
      iframe.style.width = '1200px';
      iframe.style.height = '800px';
      iframe.style.border = 'none';

      document.body.appendChild(iframe);

      return new Promise<Blob | null>((resolve) => {
        iframe.onload = async () => {
          try {
            // 获取iframe的文档内容
            const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
            if (!iframeDoc) {
              throw new Error('无法访问iframe文档');
            }

            // 等待一段时间让内容完全加载
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 使用 snapdom 截图iframe中的body
            const result = await snapdom(iframeDoc.body, {
              scale: 1 // 1倍清晰度
            });

            // 根据格式生成对应的 Blob
            const canvas = await result.toCanvas();
            canvas.toBlob((blob) => {
              // 清理iframe
              document.body.removeChild(iframe);
              resolve(blob);
            }, format === 'jpg' ? 'image/jpeg' : 'image/png', format === 'jpg' ? 0.9 : undefined);

          } catch (error) {
            console.error('截图失败:', error);
            document.body.removeChild(iframe);
            resolve(null);
          }
        };

        // 设置iframe内容
        iframe.srcdoc = fullHtml;
      });

    } catch (error) {
      console.error('❌ 图片生成失败:', error)
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: '图片生成失败，请重试',
        messageType: 'error',
        duration: 5000
      }, '*')
      recoverScrollTop?.()
      return null
    }
  }

  if (!supported) {
    window.postMessage({
      type: 'FEISHU_EXPORT_MESSAGE',
      message: '当前浏览器不支持文件保存功能，请使用现代浏览器',
      messageType: 'error',
      duration: 5000
    }, '*')
    recoverScrollTop?.()
    return
  }

  try {
    // 第一阶段：准备数据
    window.postMessage({
      type: 'FEISHU_EXPORT_MESSAGE',
      message: "正在准备截图数据...",
      messageType: 'info',
      duration: 0,
      key: 'img-export'
    }, '*')

    // 第二阶段：截图处理
    window.postMessage({
      type: 'FEISHU_EXPORT_MESSAGE',
      message: "正在生成高清截图...",
      messageType: 'info',
      duration: 0,
      key: 'img-export'
    }, '*')

    // 第三阶段：生成图片文件
    window.postMessage({
      type: 'FEISHU_EXPORT_MESSAGE',
      message: "正在生成图片文件...",
      messageType: 'info',
      duration: 0,
      key: 'img-export'
    }, '*')

    // 生成图片内容
    const blob = await toBlobContent()

    // 检查blob是否成功生成
    if (!blob) {
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: '图片生成失败，请重试',
        messageType: 'error',
        duration: 5000
      }, '*')
      recoverScrollTop?.()
      return
    }

    // 显示文件大小信息
    const fileSizeMB = (blob.size / 1024 / 1024).toFixed(2)
    console.error(`🖼️ 图片文件大小: ${fileSizeMB}MB`)

    // 检查用户激活状态，如果失效则显示确认对话框
    if (!navigator.userActivation?.isActive) {
      // 用户激活状态失效时，显示确认消息
      const confirmed = await new Promise<boolean>((resolve) => {
        // 监听用户操作结果
        const handleAction = (event: MessageEvent) => {
          if (event.data && event.data.type === 'MESSAGE_ACTION') {
            window.removeEventListener('message', handleAction)
            if (event.data.action === 'confirm') {
              resolve(true)
            } else {
              resolve(false)
            }
          }
        }

        window.addEventListener('message', handleAction)

        // 显示确认消息
        window.postMessage({
          type: 'FEISHU_EXPORT_MESSAGE',
          message: `图片已准备完成 (${fileSizeMB}MB)，是否保存${format.toUpperCase()}图片？`,
          messageType: 'info',
          duration: 0,
          showConfirm: true,
          confirmButtonText: '立即保存',
          cancelButtonText: '取消导出',
          key: 'img-export-confirm' // 使用独立的key，避免被后续消息替换
        }, '*')
      })

      if (!confirmed) {
        window.postMessage({
          type: 'FEISHU_EXPORT_MESSAGE',
          message: '导出已取消',
          messageType: 'info',
          duration: 3000,
          key: 'img-export'
        }, '*')
        recoverScrollTop?.()
        return
      }
    } else {
      // 用户激活状态正常，显示即将保存的提示
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: `图片准备完成 (${fileSizeMB}MB)，即将保存文件...`,
        messageType: 'info',
        duration: 2000
      }, '*')
    }

    // 大文件处理：添加保存进度提示
    if (blob.size > 10 * 1024 * 1024) { // 大于10MB显示特殊提示
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: `正在保存大图片 (${fileSizeMB}MB)，请耐心等待...`,
        messageType: 'info',
        duration: 0,
        key: 'img-export'
      }, '*')
    }

    // 使用Promise.race添加超时处理
    const savePromise = fileSave(blob, {
      fileName: filename,
      extensions: [extension],
    })

    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('文件保存超时，请重试')), 60000) // 60秒超时
    })

    await Promise.race([savePromise, timeoutPromise])

    // 先执行滚动恢复，再显示成功消息，避免消息被页面操作影响
    recoverScrollTop?.()

    // 添加短暂延迟确保页面稳定后再显示消息
    setTimeout(() => {
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: `${format.toUpperCase()}图片导出成功！`,
        messageType: 'success',
        duration: 5000,
        key: 'img-export'
      }, '*')
    }, 100)

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'

    if (errorMessage.includes('user gesture') || errorMessage.includes('User activation')) {
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: '文件保存需要用户操作，请重新点击导出按钮',
        messageType: 'warning',
        duration: 5000
      }, '*')
    } else {
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: `文件保存失败: ${errorMessage}`,
        messageType: 'error',
        duration: 5000
      }, '*')
    }

    recoverScrollTop?.()
  }
}

// 便捷的导出函数
export const exportToPng = () => exportImgInjected('png');
export const exportToJpg = () => exportImgInjected('jpg');