import { Toast } from "@/pkg/lark/env";
import { prepareExportDataForPdf } from "../export-pdf/prepare-util";
import { confirmWithCancel } from '@/pkg/utils/notification'
import { fileSave, supported } from 'browser-fs-access'
import { convertDocxToWordDocument } from "./word-converter";
import { docx } from "@/pkg/lark/docx";
import { Packer } from 'docx'

export async function exportWordInjected() {
  // 显示开始导出消息
  window.postMessage({
    type: 'FEISHU_EXPORT_MESSAGE',
    message: '开始导出Word文档...',
    messageType: 'info',
    duration: 0, // 不自动隐藏，等待后续状态更新
    key: 'word-export' // 添加固定的key，确保消息能被正确替换
  }, '*')

  // 使用PDF导出的数据准备方法
  const data = await prepareExportDataForPdf()
  if (!data) {
    window.postMessage({
      type: 'FEISHU_EXPORT_MESSAGE',
      message: '数据准备失败，请重试',
      messageType: 'error',
      duration: 5000
    }, '*')
    return
  }

  const { recommendName, recoverScrollTop } = data

  // 生成带时间戳的文件名
  const now = new Date()
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const hour = now.getHours().toString().padStart(2, '0')
  const minute = now.getMinutes().toString().padStart(2, '0')
  const second = now.getSeconds().toString().padStart(2, '0')
  const timestamp = `${year}${month}${day}_${hour}${minute}${second}`
  const filename = `${recommendName}_${timestamp}.docx`

  const toBlobContent = async (): Promise<Blob | null> => {
    // 第一阶段：准备数据
    window.postMessage({
      type: 'FEISHU_EXPORT_MESSAGE',
      message: '正在准备Word文档数据',
      messageType: 'info',
      duration: 0,
      key: 'word-export'
    }, '*')

    // 第二阶段：直接从rootblock转换为Word文档
    window.postMessage({
      type: 'FEISHU_EXPORT_MESSAGE',
      message: '正在处理文档内容和图片',
      messageType: 'info',
      duration: 0,
      key: 'word-export'
    }, '*')

    // 第三阶段：生成Word文件
    window.postMessage({
      type: 'FEISHU_EXPORT_MESSAGE',
      message: '正在生成Word文件',
      messageType: 'info',
      duration: 0,
      key: 'word-export'
    }, '*')

    try {
      // 直接从rootblock转换为Word文档
      const doc = await convertDocxToWordDocument(docx.rootBlock, {
        convertImages: true,
        convertFiles: false, // Word导出不处理文件下载
      })

      // 生成blob
      const buffer = await Packer.toBlob(doc)
      return buffer
    } catch (error) {
      console.error('❌ docx转换失败:', error)
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: 'Word文档生成失败，请重试',
        messageType: 'error',
        duration: 5000
      }, '*')
      recoverScrollTop?.()
      return null
    }
  }

  if (!supported) {
    window.postMessage({
      type: 'FEISHU_EXPORT_MESSAGE',
      message: '当前浏览器不支持文件保存功能，请使用现代浏览器',
      messageType: 'error',
      duration: 5000
    }, '*')
    recoverScrollTop?.()
    return
  }

  try {
    // 先生成所有内容
    const blob = await toBlobContent()

    // 检查blob是否成功生成
    if (!blob) {
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: 'Word文档生成失败，请重试',
        messageType: 'error',
        duration: 5000
      }, '*')
      recoverScrollTop?.()
      return
    }

    // 显示文件大小信息
    const fileSizeMB = (blob.size / 1024 / 1024).toFixed(2)
    console.error(`📄 Word文件大小: ${fileSizeMB}MB`)

    // 检查用户激活状态，如果失效则显示确认对话框
    if (!navigator.userActivation?.isActive) {
      // 用户激活状态失效时，显示确认消息
      const confirmed = await new Promise<boolean>((resolve) => {
        // 监听用户操作结果
        const handleAction = (event: MessageEvent) => {
          if (event.data && event.data.type === 'MESSAGE_ACTION') {
            window.removeEventListener('message', handleAction)
            if (event.data.action === 'confirm') {
              resolve(true)
            } else {
              resolve(false)
            }
          }
        }

        window.addEventListener('message', handleAction)

        // 显示确认消息
        window.postMessage({
          type: 'FEISHU_EXPORT_MESSAGE',
          message: `Word文档已准备完成 (${fileSizeMB}MB)，是否立即保存？`,
          messageType: 'info',
          duration: 0,
          showConfirm: true,
          confirmButtonText: '立即保存',
          cancelButtonText: '取消导出',
          key: 'word-export-confirm' // 使用独立的key，避免被后续消息替换
        }, '*')
      })

      if (!confirmed) {
        window.postMessage({
          type: 'FEISHU_EXPORT_MESSAGE',
          message: '导出已取消',
          messageType: 'info',
          duration: 3000,
          key: 'word-export'
        }, '*')
        recoverScrollTop?.()
        return
      }
    } else {
      // 用户激活状态正常，显示即将保存的提示
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: `Word文档准备完成 (${fileSizeMB}MB)，即将保存文件...`,
        messageType: 'info',
        duration: 2000
      }, '*')
    }

    await fileSave(blob, {
      fileName: filename,
      extensions: ['.docx'],
    })

    // 先执行滚动恢复，再显示成功消息，避免消息被页面操作影响
    recoverScrollTop?.()

    // 添加短暂延迟确保页面稳定后再显示消息
    setTimeout(() => {
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: 'Word文档导出成功！',
        messageType: 'success',
        duration: 5000,
        key: 'word-export'
      }, '*')
    }, 100)

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'

    if (errorMessage.includes('user gesture') || errorMessage.includes('User activation')) {
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: '文件保存需要用户操作，请重新点击导出按钮',
        messageType: 'warning',
        duration: 5000
      }, '*')
    } else {
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: `文件保存失败: ${errorMessage}`,
        messageType: 'error',
        duration: 5000
      }, '*')
    }

    recoverScrollTop?.()
  }
}