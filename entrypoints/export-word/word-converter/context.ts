import { Blocks } from '@/pkg/lark/docx'
import { Paragraph, Table } from 'docx'

/**
 * Word转换选项
 */
export interface WordOptions {
  /** 是否转换图片 */
  convertImages: boolean
  /** 是否转换文件 */
  convertFiles: boolean
  /** 图片最大宽度（像素） */
  maxImageWidth: number
  /** 图片最大高度（像素） */
  maxImageHeight: number
  /** 是否保持原始图片尺寸比例 */
  keepImageAspectRatio: boolean
}

/**
 * Word转换结果
 */
export interface WordTransformResult {
  /** 转换后的文档元素 */
  elements: Array<Paragraph | Table>
  /** 图片信息 */
  images: Array<{
    token: string
    name: string
    dataUrl?: string
    width?: number
    height?: number
  }>
  /** 文件信息 */
  files: Array<{
    token: string
    name: string
    url?: string
  }>
}

/**
 * 块转换器函数类型
 */
export type BlockWordTransformer = (block: Blocks, context: WordContext) => Paragraph | Table | Array<Paragraph | Table> | null

/**
 * Word转换上下文
 */
export interface WordContext {
  /** 转换选项 */
  options: WordOptions
  /** 图片信息收集 */
  images: Array<{
    token: string
    name: string
    dataUrl?: string
    width?: number
    height?: number
  }>
  /** 文件信息收集 */
  files: Array<{
    token: string
    name: string
    url?: string
  }>
  /** 当前列表层级 */
  listLevel: number
  /** 当前列表类型栈 */
  listTypeStack: string[]
  /** 当前有序列表编号栈 */
  orderedListCounters: number[]
  /** 是否在表格单元格中 */
  inTableCell: boolean
}

/**
 * 默认转换选项
 */
const defaultWordOptions: WordOptions = {
  convertImages: true,
  convertFiles: false,
  maxImageWidth: 400,
  maxImageHeight: 600,
  keepImageAspectRatio: true,
}

/**
 * 创建Word转换上下文
 */
export function createWordContext(options: Partial<WordOptions> = {}): WordContext {
  return {
    options: { ...defaultWordOptions, ...options },
    images: [],
    files: [],
    listLevel: 0,
    listTypeStack: [],
    orderedListCounters: [],
    inTableCell: false,
  }
}

/**
 * 添加图片到上下文
 */
export function addImageToContext(
  context: WordContext,
  token: string,
  name: string,
  dataUrl?: string,
  width?: number,
  height?: number
): void {
  const existingImage = context.images.find(img => img.token === token)
  if (!existingImage) {
    context.images.push({
      token,
      name,
      dataUrl,
      width,
      height,
    })
  }
}

/**
 * 添加文件到上下文
 */
export function addFileToContext(
  context: WordContext,
  token: string,
  name: string,
  url?: string
): void {
  const existingFile = context.files.find(file => file.token === token)
  if (!existingFile) {
    context.files.push({
      token,
      name,
      url,
    })
  }
}

/**
 * 进入列表层级
 */
export function enterListLevel(context: WordContext, listType: string): void {
  context.listLevel++
  context.listTypeStack.push(listType)

  if (listType === 'ordered') {
    context.orderedListCounters.push(1)
  }
}

/**
 * 退出列表层级
 */
export function exitListLevel(context: WordContext): void {
  if (context.listLevel > 0) {
    context.listLevel--
    const listType = context.listTypeStack.pop()

    if (listType === 'ordered') {
      context.orderedListCounters.pop()
    }
  }
}

/**
 * 获取当前有序列表编号并递增
 */
export function getAndIncrementOrderedListCounter(context: WordContext): number {
  const currentLevel = context.orderedListCounters.length - 1
  if (currentLevel >= 0) {
    const counter = context.orderedListCounters[currentLevel]
    context.orderedListCounters[currentLevel]++
    return counter
  }
  return 1
}

/**
 * 计算图片在Word文档中的合适尺寸
 */
export function calculateImageDimensions(
  originalWidth: number,
  originalHeight: number,
  context: WordContext
): { width: number; height: number } {
  let { maxImageWidth, maxImageHeight, keepImageAspectRatio } = context.options

  // 如果在表格单元格中，使用更小的尺寸限制
  if (context.inTableCell) {
    maxImageWidth = Math.min(maxImageWidth, 120) // 表格中图片最大宽度120像素
    maxImageHeight = Math.min(maxImageHeight, 160) // 表格中图片最大高度160像素
  }

  if (!keepImageAspectRatio) {
    const result = {
      width: Math.min(originalWidth, maxImageWidth),
      height: Math.min(originalHeight, maxImageHeight)
    }
    return result
  }

  // 计算缩放比例以保持宽高比
  const widthScale = maxImageWidth / originalWidth
  const heightScale = maxImageHeight / originalHeight
  const scale = Math.min(widthScale, heightScale, 1) // 不放大，只缩小

  const targetWidth = Math.round(originalWidth * scale)
  const targetHeight = Math.round(originalHeight * scale)

  return {
    width: targetWidth,
    height: targetHeight
  }
}

/**
 * 从图片缓冲区数据中获取原始尺寸
 */
export async function getImageDimensionsFromBuffer(buffer: Uint8Array): Promise<{ width: number; height: number } | null> {
  try {
    // 创建blob和临时URL来获取图片尺寸
    const blob = new Blob([buffer])
    const url = URL.createObjectURL(blob)

    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        URL.revokeObjectURL(url)
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight
        })
      }
      img.onerror = () => {
        URL.revokeObjectURL(url)
        resolve(null)
      }
      img.src = url
    })
  } catch (error) {
    console.error('从缓冲区获取图片尺寸失败:', error)
    return null
  }
}
