import { Blocks, BlockType } from '@/pkg/lark/docx'
import { WordContext } from '../context'
import { Table, TableRow, TableCell, Paragraph, TextRun } from 'docx'
import { transformBlock } from '../transform'

/**
 * 转换表格块
 */
export async function transformTable(block: Blocks, context: WordContext): Promise<Table | null> {
  if (block.type === BlockType.TABLE) {
    return await transformTableBlock(block, context)
  } else if (block.type === BlockType.CELL) {
    // 单元格块不应该单独转换，而是在表格内部处理
    return null
  }

  return null
}

/**
 * 转换表格块
 */
async function transformTableBlock(block: Blocks, context: WordContext): Promise<Table | null> {
  if (block.type !== BlockType.TABLE) return null

  try {
    const { columns_id, rows_id } = (block.snapshot as any) || {}
    const columnCount = columns_id?.length || 0
    const rowCount = rows_id?.length || 0

    if (columnCount === 0 || rowCount === 0) {
      return null
    }

    // 从子块中获取所有单元格块
    const cellBlocks = block.children.filter(child => child.type === BlockType.CELL)

    if (cellBlocks.length === 0) {
      return null
    }

    // 将单元格块按行分组
    const rows: Blocks[][] = []
    for (let i = 0; i < rowCount; i++) {
      const rowCells = cellBlocks.slice(i * columnCount, (i + 1) * columnCount)
      rows.push(rowCells)
    }

    const tableRows: TableRow[] = []

    // 处理每一行
    for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
      const rowCells = rows[rowIndex]
      const cells: TableCell[] = []
      const isHeaderRow = rowIndex === 0 // 第一行作为表头

      // 处理每个单元格
      for (const cellBlock of rowCells) {
        const cell = await transformCellBlock(cellBlock, context, isHeaderRow)
        if (cell) {
          cells.push(cell)
        }
      }

      if (cells.length > 0) {
        tableRows.push(new TableRow({ children: cells }))
      }
    }

    if (tableRows.length === 0) {
      return null
    }

    return new Table({
      rows: tableRows,
      // 表格整体样式
      width: {
        size: 100,
        type: 'pct', // 100% 宽度
      },
      // 表格边框
      borders: {
        top: { style: 'single', size: 6, color: 'auto' },
        bottom: { style: 'single', size: 6, color: 'auto' },
        left: { style: 'single', size: 6, color: 'auto' },
        right: { style: 'single', size: 6, color: 'auto' },
        insideHorizontal: { style: 'single', size: 4, color: 'auto' },
        insideVertical: { style: 'single', size: 4, color: 'auto' },
      },
    })
  } catch (error) {
    console.error('转换表格失败:', error)
    return null
  }
}

/**
 * 转换单元格块
 */
async function transformCellBlock(block: Blocks, context: WordContext, isHeaderRow: boolean = false): Promise<TableCell | null> {
  if (block.type !== BlockType.CELL) return null

  try {
    const cellContent: (Paragraph | Table)[] = []

    // 设置表格单元格上下文
    const originalInTableCell = context.inTableCell
    context.inTableCell = true

    // 处理单元格内的子块
    if (block.children && block.children.length > 0) {
      for (const child of block.children) {
        const result = await transformBlock(child, context)
        if (result) {
          if (Array.isArray(result)) {
            cellContent.push(...result)
          } else {
            cellContent.push(result)
          }
        }
      }
    }

    // 恢复原始上下文
    context.inTableCell = originalInTableCell

    // 如果没有子块内容，尝试从当前块的 zoneState 提取文本
    if (cellContent.length === 0) {
      const textContent = extractCellTextContent(block)
      if (textContent.trim()) {
        cellContent.push(new Paragraph({
          children: [new TextRun({
            text: textContent,
            bold: isHeaderRow,
          })],
        }))
      }
    }

    // 如果还是没有内容，创建一个空段落
    if (cellContent.length === 0) {
      cellContent.push(new Paragraph({
        children: [new TextRun({ text: '' })]
      }))
    }

    // 如果是表头行，将所有文本设为粗体
    if (isHeaderRow) {
      cellContent.forEach(content => {
        if (content instanceof Paragraph) {
          // Paragraph的children属性是只读的，我们需要在创建时就设置粗体
          // 这里的逻辑已经在上面的TextRun创建时处理了
        }
      })
    }

    return new TableCell({
      children: cellContent,
      // 添加单元格内边距
      margins: {
        top: 144,    // 0.1 inch (144 twips)
        bottom: 144, // 0.1 inch 
        left: 216,   // 0.15 inch (216 twips)
        right: 216,  // 0.15 inch
      },
      // 添加边框样式
      borders: {
        top: { style: 'single', size: 4, color: 'auto' },
        bottom: { style: 'single', size: 4, color: 'auto' },
        left: { style: 'single', size: 4, color: 'auto' },
        right: { style: 'single', size: 4, color: 'auto' },
      },
      // 表头单元格使用浅灰色背景
      ...(isHeaderRow && {
        shading: {
          fill: 'F2F2F2', // 浅灰色背景
        },
      }),
    })
  } catch (error) {
    console.error('转换单元格失败:', error)
    return null
  }
}

/**
 * 从单元格块中提取文本内容
 */
function extractCellTextContent(block: Blocks): string {
  // 优先使用 zoneState.allText
  if (block.zoneState?.allText) {
    return block.zoneState.allText
  }

  // 其次使用 zoneState.content.ops
  if (block.zoneState?.content?.ops) {
    return block.zoneState.content.ops
      .map((op: any) => {
        if (typeof op.insert === 'string') {
          return op.insert
        }
        return ''
      })
      .join('')
  }

  return ''
}
