import { Blocks } from '@/pkg/lark/docx'
import { WordContext } from '../context'
import { Paragraph, TextRun } from 'docx'

/**
 * 转换代码块
 */
export function transformCode(block: Blocks, context: WordContext): Paragraph[] {
  const codeContent = extractCodeContent(block)
  const language = extractLanguage(block)
  
  const paragraphs: Paragraph[] = []

  // 如果有语言信息，添加语言标签段落
  if (language) {
    paragraphs.push(new Paragraph({
      children: [
        new TextRun({
          text: `[${language.toUpperCase()}]`,
          font: 'Consolas',
          size: 16, // 8pt字体
          color: '666666',
          bold: true,
        })
      ],
      spacing: {
        before: 240, // 段前间距 (12pt)
        after: 60,   // 与代码块的间距
      },
      indent: {
        left: 360, // 左缩进与代码块一致
      },
    }))
  }

  // 将代码按行分割
  const lines = codeContent.split('\n')
  
  // 如果没有代码内容，创建一个空的代码段落
  if (lines.length === 0 || (lines.length === 1 && lines[0] === '')) {
    paragraphs.push(new Paragraph({
      children: [
        new TextRun({
          text: ' ',
          font: 'Consolas',
          size: 20,
          color: '24292F',
        })
      ],
      spacing: {
        before: language ? 60 : 240,
        after: 240,
        line: 240,
        lineRule: 'exact',
      },
      indent: {
        left: 480,
        right: 480,
      },
      shading: {
        fill: 'F6F8FA',
      },
    }))
    return paragraphs
  }

  // 为每一行创建独立的段落
  lines.forEach((line, index) => {
    // 处理空行 - 保持制表符和空格
    const lineText = line.length === 0 ? ' ' : line
    const isFirstLine = index === 0
    const isLastLine = index === lines.length - 1

    paragraphs.push(new Paragraph({
      children: [
        new TextRun({
          text: lineText,
          font: 'Consolas', // 使用等宽字体
          size: 20, // 10pt字体 (size = pt * 2)
          color: '24292F', // 更标准的代码颜色
        })
      ],
      spacing: {
        before: isFirstLine ? (language ? 60 : 240) : 0,  // 第一行设置段前间距
        after: isLastLine ? 240 : 0,   // 最后一行设置段后间距
        line: 240,  // 行距 (12pt)
        lineRule: 'exact',
      },
      indent: {
        left: 480,  // 左缩进，为代码块留出空间
        right: 480, // 右缩进
        hanging: 0, // 无悬挂缩进
      },
      shading: {
        fill: 'F6F8FA', // 浅灰色背景
      },
    }))
  })

  return paragraphs
}

/**
 * 从代码块中提取代码内容
 */
function extractCodeContent(block: Blocks): string {
  // 优先使用zoneState.allText
  if (block.zoneState?.allText) {
    return block.zoneState.allText
  }

  // 其次使用zoneState.content.ops
  if (block.zoneState?.content?.ops) {
    return block.zoneState.content.ops
      .map(op => {
        if (typeof op.insert === 'string') {
          return op.insert
        }
        return ''
      })
      .join('')
  }

  return ''
}

/**
 * 从代码块中提取语言信息
 */
function extractLanguage(block: Blocks): string {
  // 尝试从block的属性中获取语言信息
  const codeBlock = block as any
  if (codeBlock.language) {
    return codeBlock.language
  }

  // 尝试从snapshot中获取
  if (codeBlock.snapshot?.language) {
    return codeBlock.snapshot.language
  }

  return ''
}
