import { Blocks } from '@/pkg/lark/docx'
import { WordContext, addFileToContext } from '../context'
import { Paragraph, TextRun } from 'docx'

/**
 * 转换文件块
 */
export function transformFile(block: Blocks, context: WordContext): Paragraph | null {
  const fileInfo = extractFileInfo(block)
  if (!fileInfo) {
    return null
  }

  const { token, name, url } = fileInfo

  // 添加文件到上下文
  if (context.options.convertFiles && url) {
    addFileToContext(context, token, name, url)
  }

  // 创建文件占位符段落
  return new Paragraph({
    children: [
      new TextRun({
        text: `[文件: ${name}${context.options.convertFiles ? '' : ' - Word文档中不支持文件下载'}]`,
        italics: true,
        color: context.options.convertFiles ? '0066CC' : '999999',
        underline: context.options.convertFiles ? {} : undefined,
      }),
    ],
    spacing: {
      before: 120,
      after: 120,
    },
  })
}

/**
 * 从文件块中提取文件信息
 */
function extractFileInfo(block: Blocks): {
  token: string
  name: string
  url?: string
} | null {
  try {
    const fileSnapshot = (block.snapshot as any)?.file
    if (!fileSnapshot) {
      return null
    }

    const token = fileSnapshot.token
    const name = fileSnapshot.name || '未知文件'

    if (!token) {
      return null
    }

    // 构建文件下载URL（如果需要）
    let url: string | undefined
    if (fileSnapshot.url) {
      url = fileSnapshot.url
    } else {
      // 使用新的预览下载接口（统一格式）
      url = `https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/preview/${token}?mount_point=docx_file&preview_type=16`
    }

    return {
      token,
      name,
      url,
    }
  } catch (error) {
    console.error('提取文件信息失败:', error)
    return null
  }
}
