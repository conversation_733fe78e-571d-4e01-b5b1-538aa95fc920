import { PageBlock, Blocks } from '@/pkg/lark/docx'

/**
 * 预处理图片，将图片转换为data URL
 */
export async function preprocessImages(rootBlock: PageBlock): Promise<void> {

  const imageBlocks = collectImageBlocks(rootBlock)



  if (imageBlocks.length === 0) {
    return
  }

  // 获取页面中所有图片元素，用于备用匹配
  const allPageImages = Array.from(document.querySelectorAll('img')) as HTMLImageElement[]

  // 串行处理图片，避免并发问题
  for (let index = 0; index < imageBlocks.length; index++) {
    try {
      await processImageBlock(imageBlocks[index], index + 1, imageBlocks.length, allPageImages)
    } catch (error) {
      console.error(`🖼️ 处理图片 ${index + 1} 失败:`, error)
    }
  }

}

/**
 * 收集所有图片块
 */
function collectImageBlocks(block: Blocks): Blocks[] {
  const imageBlocks: Blocks[] = []

  if (block.type === 'image') {
    imageBlocks.push(block)
  }

  if (block.children) {
    for (const child of block.children) {
      imageBlocks.push(...collectImageBlocks(child))
    }
  }

  return imageBlocks
}

/**
 * 处理单个图片块 - 直接使用imageManager获取图片源（参考HTML转换器）
 */
async function processImageBlock(block: Blocks, index: number, total: number, allPageImages?: HTMLImageElement[]): Promise<void> {

  const imageSnapshot = (block.snapshot as any)?.image
  if (!imageSnapshot) {
    return
  }


  const { token, name, width, height, scale } = imageSnapshot
  if (!token) {
    return
  }



  try {
    // 优先使用imageManager获取图片源（参考HTML转换器的方法）
    const sources = await fetchImageSourcesFromBlock(block)
    if (sources?.src) {

      // 直接fetch图片并转换为dataURL
      const response = await fetch(sources.src)
      if (response.ok) {
        const blob = await response.blob()
        const dataUrl = await blobToDataUrl(blob)
        imageSnapshot.dataUrl = dataUrl
        return
      } else {
        console.error(`🖼️ 图片 ${index} imageManager源下载失败，状态码: ${response.status}`)
      }
    } else {
      console.error(`🖼️ 图片 ${index} 无法通过imageManager获取图片源`)
    }

    // 回退方法：尝试从页面中查找图片元素
    let imgElement = findImageElement(token, name, index, allPageImages)

    if (!imgElement) {
      return
    }

    // 如果图片已经是data URL，直接使用
    if (imgElement.src.startsWith('data:')) {
      imageSnapshot.dataUrl = imgElement.src
      return
    }

    // 尝试fetch页面图片元素的src
    if (imgElement.src && !imgElement.src.startsWith('data:')) {
      try {
        const response = await fetch(imgElement.src)
        if (response.ok) {
          const blob = await response.blob()
          const dataUrl = await blobToDataUrl(blob)
          imageSnapshot.dataUrl = dataUrl
        } else {
          console.error(`🖼️ 图片 ${index} 页面元素src下载失败，状态码: ${response.status}`)
        }
      } catch (fetchError) {
        console.error(`🖼️ 图片 ${index} 页面元素src转换失败:`, fetchError)
      }
    }
  } catch (error) {
    console.error(`🖼️ 处理图片 ${index} 时出错:`, error)
  }
}



/**
 * 从data URL中提取图片数据
 */
export function extractImageDataFromDataUrl(dataUrl: string): {
  buffer: Uint8Array
  mimeType: string
  type: 'png' | 'jpg' | 'gif' | 'bmp'
} | null {
  try {
    if (!dataUrl.startsWith('data:')) {
      return null
    }

    // 提取MIME类型和base64数据
    const [mimeInfo, base64Data] = dataUrl.split(',')
    const mimeType = mimeInfo.match(/data:([^;]+)/)?.[1] || 'image/png'
    const buffer = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0))

    // 根据MIME类型确定图片格式
    let type: 'png' | 'jpg' | 'gif' | 'bmp' = 'png'
    if (mimeType.includes('jpeg') || mimeType.includes('jpg')) {
      type = 'jpg'
    } else if (mimeType.includes('gif')) {
      type = 'gif'
    } else if (mimeType.includes('bmp')) {
      type = 'bmp'
    }

    return {
      buffer,
      mimeType,
      type
    }
  } catch (error) {
    console.error('从data URL提取图片数据失败:', error)
    return null
  }
}

/**
 * 智能查找图片元素
 */
function findImageElement(token: string, name: string, index: number, allPageImages?: HTMLImageElement[]): HTMLImageElement | null {

  // 尝试多种选择器
  const selectors = [
    `img[src*="${token}"]`,
    `img[data-token="${token}"]`,
    `img[data-file-token="${token}"]`,
    `[data-token="${token}"] img`,
    `[data-file-token="${token}"] img`,
  ]

  for (const selector of selectors) {
    const element = document.querySelector(selector) as HTMLImageElement
    if (element) {
      return element
    }
  }

  // 如果通过token找不到，尝试通过文件名匹配
  if (name) {
    const imagesToSearch = allPageImages || Array.from(document.querySelectorAll('img'))
    for (const img of imagesToSearch) {
      const src = img.getAttribute('src') || ''
      const alt = img.getAttribute('alt') || ''
      const title = img.getAttribute('title') || ''

      // 检查src、alt或title中是否包含文件名
      if (src.includes(name) || alt.includes(name) || title.includes(name)) {
        return img
      }
    }
  }

  // 最后尝试：按索引顺序匹配（假设图片在页面中的顺序与rootblock中的顺序一致）
  const imagesToSearch = allPageImages || Array.from(document.querySelectorAll('img'))
  if (index <= imagesToSearch.length) {
    const imgByIndex = imagesToSearch[index - 1]
    if (imgByIndex) {
      return imgByIndex
    }
  }



  return null
}

/**
 * 通过imageManager获取图片源（参考HTML转换器的方法）
 */
async function fetchImageSourcesFromBlock(block: Blocks): Promise<{ src: string; originSrc: string } | null> {
  try {
    const imageManager = (block as any).imageManager
    if (!imageManager?.fetch) {
      return null
    }

    const imageData = (block.snapshot as any)?.image
    if (!imageData?.token) {
      return null
    }

    // 使用imageManager获取图片源
    const sources = await imageManager.fetch(
      { token: imageData.token, isHD: false },
      {},
      (sources: { originSrc: string; src: string }) => sources
    )

    return sources
  } catch (error) {
    return null
  }
}

/**
 * 将Blob转换为data URL
 */
function blobToDataUrl(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = reject
    reader.readAsDataURL(blob)
  })
}

/**
 * 获取图片的原始尺寸
 * 优先使用 imageSnapshot 中的尺寸信息，然后再从 dataUrl 获取
 */
export async function getImageDimensions(dataUrl: string, imageSnapshot?: any): Promise<{ width: number; height: number } | null> {
  try {
    // 优先使用 imageSnapshot 中的尺寸信息
    if (imageSnapshot?.width && imageSnapshot?.height) {
      const scale = imageSnapshot.scale || 1
      const actualWidth = Math.round(imageSnapshot.width * scale)
      const actualHeight = Math.round(imageSnapshot.height * scale)



      return {
        width: actualWidth,
        height: actualHeight
      }
    }


    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight
        })
      }
      img.onerror = () => {
        resolve(null)
      }
      img.src = dataUrl
    })
  } catch (error) {
    return null
  }
}
