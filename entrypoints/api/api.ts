import { downloadStatsClient, FileType } from "../utils/download-stats";

// 添加统计记录函数
export const recordDownloadStats = async (action: string, currentUrl?: string): Promise<void> => {
  try {
    // 操作类型到文件类型的映射
    const actionToFileTypeMap: Record<string, FileType> = {
      'exportPdf': 'PDF',
      'exportCollect': 'PDF',
      'exportWord': 'WORD',
      'exportMd': 'MARKDOWN',
      'exportHtml': 'HTML',
      'exportImg': 'IMAGE',
      'copyToFeishu': 'OTHER',
      'exportPdfAll': 'PDF',
      'breakCopy': 'OTHER',
      'preview2': 'OTHER'
    };

    const fileType = actionToFileTypeMap[action];
    if (!fileType) {
      console.warn(`未知的操作类型: ${action}`);
      return;
    }

    // 如果没有提供URL，尝试获取当前活动tab的URL
    let downloadUrl = currentUrl;
    if (!downloadUrl) {
      try {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        });
        downloadUrl = activeTabs[0]?.url;
      } catch (error) {
        console.warn('获取当前页面URL失败:', error);
      }
    }
    console.log('downloadUrl', downloadUrl)
    const params = {
      fileType,
      fileName: `${action}_${new Date().toISOString()}`,
      downloadUrl
    };

    const result = await downloadStatsClient.recordDownload(params);
    if (result.success) {
      console.log('下载统计记录成功:', action);
    } else {
      console.warn('下载统计记录失败:', result.message);
    }
  } catch (error) {
    console.error('下载统计记录异常:', error);
  }
};