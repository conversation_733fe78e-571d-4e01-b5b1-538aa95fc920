import { operationStatsClient, OperationType } from "../utils/download-stats";

// 添加操作记录函数
export const recordOperationStats = async (action: string, currentUrl?: string): Promise<void> => {
  try {
    // 操作类型到操作类型的映射
    const actionToOperationTypeMap: Record<string, OperationType> = {
      'exportPdf': 'PDF导出',
      'exportCollect': 'PDF导出',
      'exportWord': 'Word导出',
      'exportMd': 'Markdown导出',
      'exportHtml': 'HTML导出',
      'exportImg': '图片导出',
      'copyToFeishu': '复制到飞书',
      'exportPdfAll': '批量导出',
      'breakCopy': '复制到飞书',
      'preview2': '文档查看'
    };

    const operationType = actionToOperationTypeMap[action];
    if (!operationType) {
      console.warn(`未知的操作类型: ${action}`);
      return;
    }

    // 如果没有提供URL，尝试获取当前活动tab的URL
    let documentUrl = currentUrl;
    if (!documentUrl) {
      try {
        const activeTabs = await browser.tabs.query({
          currentWindow: true,
          active: true,
        });
        documentUrl = activeTabs[0]?.url;
      } catch (error) {
        console.warn('获取当前页面URL失败:', error);
      }
    }
    console.log('documentUrl', documentUrl)
    const params = {
      operation_time: new Date().toISOString(),
      operation_type: operationType,
      document_name: `${action}_${new Date().toISOString()}`,
      document_url: documentUrl
    };

    const result = await operationStatsClient.recordOperation(params);
    if (result.success) {
      console.log('操作统计记录成功:', action);
    } else {
      console.warn('操作统计记录失败:', result.message);
    }
  } catch (error) {
    console.error('操作统计记录异常:', error);
  }
};