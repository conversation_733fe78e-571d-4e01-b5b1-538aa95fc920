import { exportCollectInjected } from "./collect/collect-util";
import { exportHtmlInjected } from "./export-html/html-util";
import { exportImgInjected } from "./export-img/img-util";
import { exportMdInjected } from "./export-md/md-util";
import { exportPdfAllInjected } from "./export-pdf/pdf-export-batch";
import { exportPdfInjected, } from "./export-pdf/pdf-util";
import { exportWordInjected } from "./export-word/word-util";

export default defineUnlistedScript(async () => {
  if (window.exportConfig) {
    const { type } = window.exportConfig;
    if (type === 'single') {
      // 单个文档导出逻辑
      await exportPdfInjected("preview")
    } else if (type === 'word') {
      // word导出逻辑
      await exportWordInjected()
    } else if (type === 'md') {
      // md导出逻辑
      await exportMdInjected()
    } else if (type === 'copyToFeishu') {
      // 显示开始提示
      window.postMessage({
        type: 'FEISHU_EXPORT_MESSAGE',
        message: '开始复制文档到飞书...',
        messageType: 'info',
        duration: 0, // 不自动隐藏，等待后续状态更新
        key: 'copy-to-feishu' // 添加固定的key，确保消息能被正确替换
      }, '*')

      // 动态导入需要的模块
      const { docx } = await import("@/pkg/lark/docx");
      const { convertDocxToHtmlForPdf } = await import("@/pkg/lark/html-converter-fn");
      const { prepareExportDataForPdf } = await import("./export-pdf/prepare-util");

      // 从当前URL提取文档ID
      const currentUrl = window.location.href;

      try {

        // 使用PDF导出的数据准备逻辑
        const data = await prepareExportDataForPdf();
        if (!data) {
          console.error("❌ 数据准备失败");
          return;
        }

        // 使用HTML转换器获取真实的HTML内容
        const htmlResult = await convertDocxToHtmlForPdf(docx.rootBlock, {
          useInlineStyles: false,
          cssClassPrefix: "feishu",
          convertImages: true,
          convertFiles: true,
          generateToc: false,
          tocTitle: "目录",
          tocMaxLevel: 6,
        });

        // 显示转换完成提示
        window.postMessage({
          type: 'FEISHU_EXPORT_MESSAGE',
          message: '文档内容转换完成，正在上传...',
          messageType: 'info',
          duration: 0, // 不自动隐藏，等待后续状态更新
          key: 'copy-to-feishu'
        }, '*')

        // 给 content script 发消息，包含HTML内容
        window.postMessage({
          action: 'copyToFeishu',
          currentUrl,
          htmlContent: htmlResult.htmlWithToc || htmlResult.html,
          title: data.recommendName,
        }, '*');
        return;
      } catch (error) {
        console.error("❌ 获取HTML内容失败:", error);

        // 显示错误提示
        window.postMessage({
          type: 'FEISHU_EXPORT_MESSAGE',
          message: '文档转换失败，请重试',
          messageType: 'error',
          duration: 5000,
          key: 'copy-to-feishu'
        }, '*')


        return;
      }
    } else if (type === 'exportHtml') {
      // html导出逻辑
      await exportHtmlInjected()
    } else if (type === 'exportImg') {
      // 图片导出逻辑
      await exportImgInjected()
    } else if (type === 'exportCollect') {
      // 收集导出逻辑
      await exportCollectInjected()
    } else if (type === 'exportPdfAll') {
      // 批量导出逻辑
      await exportPdfAllInjected()
    }else if(type === 'preview2'){
      // 预览逻辑
      await exportPdfInjected("preview2")
    }
  }


});