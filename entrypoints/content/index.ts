export default defineContentScript({
  matches: [
    "https://*.feishu.cn/*",
    "https://*.feishu.net/*",
    "https://*.larksuite.com/*",
    "https://*.feishu-pre.net/*",
    "https://*.larkoffice.com/*",
  ],
  runAt: "document_end",
  async main() {
    // 监听来自页面的消息
    window.addEventListener('message', (event) => {
      if (event.data.action === 'preview') {
        browser.runtime.sendMessage({
          action: 'preview',
          data: event.data.data,
          title: event.data.title,
          originalUrl: event.data.originalUrl
        })
      } else if (event.data.action === 'copyToFeishu') {
        browser.runtime.sendMessage({
          action: 'copyToFeishuFromContent',
          currentUrl: event.data.currentUrl,
          htmlContent: event.data.htmlContent,
          title: event.data.title
        })
      } else if (event.data.type === 'collectData') {
        browser.runtime.sendMessage({
          action: 'collectData',
          data: event.data.data
        })
      }else if(event.data.action === 'preview2'){
        browser.runtime.sendMessage({
          action: 'breakCopy2',
          data: event.data.data,
          title: event.data.title,
          originalUrl: event.data.originalUrl
        })
      } else if(event.data.action === 'exportPdfNew'){
         browser.runtime.sendMessage({
          action: 'exportPdfNew',
          htmlContent: event.data.data, // 将data重命名为htmlContent以匹配background的期望
          title: event.data.title,
          originalUrl: event.data.originalUrl,
          needStats: true // 添加统计标记
        })
       }
    }) 
  },
});
