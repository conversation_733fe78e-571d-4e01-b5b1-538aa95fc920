import { logger } from "@/pkg/utils/logger";
import { prepareExportDataForPdf } from "../export-pdf/prepare-util";
import { docx } from "@/pkg/lark/docx";
import { Toast } from "@/pkg/lark/env";
import { exportOriginalPDF } from "../export-pdf/origin-pdf-export";

export const exportCollectInjected = async () => {
  // 获取head里面的name="title" 的meta 标签的content属性
  const title = document
    .querySelector('meta[name="title"]')
    ?.getAttribute("content");
  const title2 = document.querySelector("title")?.textContent;
  const isPdfFile1 = title && title.endsWith(".pdf");
  const isPdfFile2 = title2 && title2.includes(".pdf");
  if (isPdfFile1 || isPdfFile2) {
    console.error("导出原pdf文档");
    await exportOriginalPDF();
    return;
  }

  // 使用PDF专用的优化数据准备逻辑（不需要Markdown AST转换和图片URL生成）
  const data = await prepareExportDataForPdf();
  if (!data) return;

  // 收集白板数据
  const whiteboardDataMap = await collectWhiteboardData();

  const cleanedData = detailContent(docx.rootBlock, whiteboardDataMap)
  console.error('docx.pageTitle', docx.pageTitle)
  window.postMessage({
    type: 'collectData',
    data: {
      pageTitle: docx.pageTitle,
      pageContent: cleanedData,
      originalUrl: window.location.href,
    },
  }, '*')
}

/**
 * 收集白板数据，将白板转换为图片 dataURL
 */
async function collectWhiteboardData(): Promise<Map<string, string>> {
  const whiteboardMap = new Map<string, string>();

  if (!docx.rootBlock) {
    return whiteboardMap;
  }

  // 启用白板转换获取 AST，参考 md-util.ts 的方式
  const { images } = docx.intoMarkdownAST({
    whiteboard: true,
    file: false,
  });

  // 筛选出白板图片（有 fetchBlob 的是白板）
  const whiteboardImages = images.filter(image => image.data?.fetchBlob);

  if (whiteboardImages.length === 0) {
    return whiteboardMap;
  }

  Toast.loading({
    content: `正在处理白板 (0/${whiteboardImages.length})...`,
    keepAlive: true,
    key: 'collect-whiteboard',
  });

  // 逐个处理白板图片（参考 md-util.ts 中白板必须逐个下载）
  for (let i = 0; i < whiteboardImages.length; i++) {
    const image = whiteboardImages[i];

    try {
      if (image.data?.fetchBlob) {
        // 获取白板的 Blob 数据
        const blob = await image.data.fetchBlob();
        if (blob) {
          // 将 Blob 转换为 base64 dataURL
          const dataUrl = await blobToDataUrl(blob);

          // 使用白板的 alt 文本作为 key，如果没有则使用索引
          const key = image.alt || `whiteboard-${i}`;
          whiteboardMap.set(key, dataUrl);

          logger.error(`白板 ${i + 1} 收集成功: ${key}`);
        }
      }
    } catch (error) {
      logger.error(`白板 ${i + 1} 处理失败:`, error);
    }

    // 更新进度
    Toast.loading({
      content: `正在处理白板 (${i + 1}/${whiteboardImages.length})...`,
      keepAlive: true,
      key: 'collect-whiteboard',
    });
  }

  Toast.remove('collect-whiteboard');

  if (whiteboardMap.size > 0) {
    Toast.success({
      content: `成功收集 ${whiteboardMap.size} 个白板`,
      duration: 2000,
    });
  }

  return whiteboardMap;
}

/**
 * 将 Blob 转换为 base64 dataURL
 */
function blobToDataUrl(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

// 入参 docx.rootBlock 出参清洗后的docx.rootBlock数据
function detailContent(rootBlock: any, whiteboardDataMap: Map<string, string>) {
  if (!rootBlock) return null;

  // 用于清洗单个块的函数
  function cleanBlock(block: any): any {
    if (!block) return null;

    // 基础块结构
    const cleanedBlock: any = {
      id: block.id,
      type: block.type,
    };

    // 保留文本内容
    if (block.zoneState?.allText) {
      cleanedBlock.zoneState = {
        allText: block.zoneState.allText,
      };

      // 保留操作数据，用于文本格式化
      if (block.zoneState.content?.ops) {
        cleanedBlock.zoneState.content = {
          ops: block.zoneState.content.ops.map((op: any) => ({
            insert: op.insert,
            attributes: op.attributes
          }))
        };
      }
    }

    // 保留完整的snapshot数据
    if (block.snapshot) {
      cleanedBlock.snapshot = { ...block.snapshot };
      // logger.error('block.snapshot', block.snapshot)
      // 处理特殊块类型的snapshot
      switch (block.type) {
        case 'heading1':
        case 'heading2':
        case 'heading3':
        case 'heading4':
        case 'heading5':
        case 'heading6':
          // 保留标题序号数据
          if (block.snapshot.seq) cleanedBlock.snapshot.seq = block.snapshot.seq;
          if (block.snapshot.seq_level) cleanedBlock.snapshot.seq_level = block.snapshot.seq_level;
          cleanedBlock.depth = block.depth;
          break;

        case 'ordered':
          // 保留有序列表序号
          if (block.snapshot.seq) cleanedBlock.snapshot.seq = block.snapshot.seq;
          break;

        case 'todo':
          // 保留待办状态
          if (block.snapshot.done !== undefined) cleanedBlock.snapshot.done = block.snapshot.done;
          break;

        case 'code':
          // 保留代码语言
          cleanedBlock.language = block.language;
          break;

        case 'table':
          // 保留表格行列信息
          if (block.snapshot.rows_id) cleanedBlock.snapshot.rows_id = block.snapshot.rows_id;
          if (block.snapshot.columns_id) cleanedBlock.snapshot.columns_id = block.snapshot.columns_id;
          break;

        case 'image':
          logger.error('block.snapshot.image', block.snapshot.image)
          // 保留图片必要信息
          if (block.snapshot.image) {
            cleanedBlock.snapshot.image = {
              token: block.snapshot.image.token,
              width: block.snapshot.image.width,
              height: block.snapshot.image.height,
              mimeType: block.snapshot.image.mimeType,
              name: block.snapshot.image.name,
              scale: block.snapshot.image.scale,
            };

            // 保留图片说明
            if (block.snapshot.image.caption) {
              cleanedBlock.snapshot.image.caption = block.snapshot.image.caption;
            }
          }
          break;

        case 'file':
          // 保留文件必要信息
          if (block.snapshot.file) {
            cleanedBlock.snapshot.file = {
              name: block.snapshot.file.name,
              token: block.snapshot.file.token
            };
          }
          break;

        case 'iframe':
          // 保留iframe必要信息
          if (block.snapshot.iframe) {
            cleanedBlock.snapshot.iframe = {};
            if (block.snapshot.iframe.height) cleanedBlock.snapshot.iframe.height = block.snapshot.iframe.height;
            if (block.snapshot.iframe.component?.url) {
              cleanedBlock.snapshot.iframe.component = {
                url: block.snapshot.iframe.component.url
              };
            }
          }
          break;

        case 'whiteboard':
          // 保留白板信息
          if (block.snapshot.caption) {
            cleanedBlock.snapshot.caption = block.snapshot.caption;
          }

          // 添加白板图片数据
          const whiteboardTitle = block.snapshot.caption?.text?.initialAttributedTexts?.text?.[0] || '';
          const whiteboardKey = whiteboardTitle || `whiteboard-${block.id}`;

          // 查找对应的白板图片数据
          let whiteboardImageData = null;
          for (const [key, dataUrl] of whiteboardDataMap.entries()) {
            if (key === whiteboardKey ||
              key.includes(whiteboardTitle) ||
              whiteboardTitle.includes(key) ||
              key === whiteboardTitle) {
              whiteboardImageData = dataUrl;
              break;
            }
          }

          // 如果没有找到匹配的，使用第一个可用的白板图片
          if (!whiteboardImageData && whiteboardDataMap.size > 0) {
            whiteboardImageData = Array.from(whiteboardDataMap.values())[0];
          }

          if (whiteboardImageData) {
            cleanedBlock.whiteboardImageData = whiteboardImageData;
            cleanedBlock.hasWhiteboardData = true;
          }
          break;
      }
    }

    // 保留record id (对于文件链接和白板很重要)
    if (block.record?.id) {
      cleanedBlock.record = { id: block.record.id };
    }

    // 递归处理子块
    if (block.children && Array.isArray(block.children) && block.children.length > 0) {
      cleanedBlock.children = block.children.map(cleanBlock).filter(Boolean);
    } else {
      cleanedBlock.children = [];
    }

    return cleanedBlock;
  }

  // 处理整个rootBlock
  return cleanBlock(rootBlock);
}