<script lang="ts" setup>
import { ref, onMounted, watch, nextTick } from "vue";
import { getTodosRepo } from "../utils/service";
import { snapdom } from "@zumer/snapdom";
import katex from 'katex';
const htmlContent = ref<string>("");
const documentTitle = ref<string>("");
const originalUrl = ref<string>("");

const loadHtmlContent = async () => {
  try {
    const todosRepo = getTodosRepo();
    const res = await todosRepo.getAll();
    const preview = res.find(item => item.id === 'breakCopy2');
    if (preview) {
      documentTitle.value = preview.title;
      document.title = preview.title + ' - 飞书转存专家';
      htmlContent.value = preview.data;
      originalUrl.value = preview.originalUrl || '';
    }
  } catch (err) {
    console.error("加载HTML内容失败:", err);
  }
};

const handlePrint = () => {
  window.print();
};

const handleImageDownload = async () => {
  const el = document.querySelector('.document-section') as HTMLElement;
  console.error('el', el)
  const result = await snapdom(el, { scale: 2 });

  const img = await result.toPng();
  document.body.appendChild(img);

  await result.download({ format: 'jpg', filename: 'my-capture' });
}

const currentTime = new Date().toLocaleString("zh-CN");

const documentSectionRef = ref<HTMLElement | null>(null)

function renderEquations() {
  if (documentSectionRef.value) {
    const nodes = documentSectionRef.value.querySelectorAll('.equation[data-equation]')
    nodes.forEach(node => {
      const latex = node.getAttribute('data-equation') || ''
      try {
        katex.render(latex, node as HTMLElement, {
          throwOnError: false,
        })
      } catch (e) {
        // 保持原样
      }
    })
  }
}

onMounted(async () => {
  await nextTick();
  loadHtmlContent();
  renderEquations();
});

watch(htmlContent, async () => {
  await nextTick();
  renderEquations();
});
</script>

<template>
  <div class="page-container">
    <!-- 左侧提示和操作区域 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo-section">
          <div class="logo-icon">📄</div>
          <div class="logo-text">
            <h1 class="app-title">飞书转存专家</h1>
            <p class="app-subtitle">强大的飞书（lark）文档转存、导出工具，一键创建文档副本到个人空间，支持PDF、Word、Markdown导出</p>
          </div>
        </div>
      </div>

      <div class="document-info">
        <h2 class="doc-title">{{ documentTitle || '文档预览' }}</h2>
        <p class="doc-meta">{{ currentTime }}</p>
      </div>

      <!-- <div class="action-section">
        <button @click="handlePrint" class="primary-btn">
          <span class="btn-icon">📄</span>
          <span class="btn-text">保存为PDF</span>
        </button>
        <button v-if="false" @click="handleImageDownload" class="secondary-btn">
          <span class="btn-icon">🖼️</span>
          <span class="btn-text">保存为图片</span>
        </button>
      </div> -->

      <!-- <div class="usage-guide">
        <h3 class="guide-title">📋 使用指南</h3>
        <div class="steps-list">
          <div class="step-item">
            <div class="step-number">1</div>
            <div class="step-content">
              <span class="step-title">点击保存为PDF</span>
              <span class="step-desc">开始导出流程</span>
            </div>
          </div>

          <div class="step-item">
            <div class="step-number">2</div>
            <div class="step-content">
              <span class="step-title">选择保存位置</span>
              <span class="step-desc">点击"保存"完成导出</span>
            </div>
          </div>
        </div>
      </div> -->



      <div class="footer-section">
        <div class="original-link">
          <span class="link-label">原文地址</span>
          <a :href="originalUrl" target="_blank" class="link-url">{{ originalUrl }}</a>
        </div>
        <div class="rating-section">
          <p class="rating-text">觉得有用？请给个五星好评 ⭐</p>
          <a href="https://chromewebstore.google.com/detail/%E9%A3%9E%E4%B9%A6%E5%8A%A9%E6%89%8B-%E9%A3%9E%E4%B9%A6lark%E5%AF%BC%E5%87%BA/cfenjfhlhjpkaaobmhbobajnnhifilbl"
            target="_blank" class="rating-link">前往Chrome商店评分</a>
        </div>
      </div>
    </div>

    <!-- 右侧文档内容区域 -->
    <div class="content-area">
      <div class="document-section" ref="documentSectionRef">
        <!-- <div class="document-header">
          <h1 class="document-title">{{ documentTitle || '文档标题' }}</h1>
          <p class="document-subtitle">本文档由浏览器扩展飞书转存专家生成</p>
        </div> -->
        <div v-html="htmlContent" class="document-content"></div>
      </div>
    </div>
  </div>
</template>
