* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", Aria<PERSON>, sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  color: #2c3e50;
  font-size: 16px;
  background: #f5f7fa;
  min-height: 100vh;
  position: relative;
}

.page-container {
  display: flex;
  min-height: 100vh;
  gap: 0;
}

/* 左侧边栏样式 */
.sidebar {
  width: 320px;
  background: #2c3e50;
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  overflow-y: auto;
  z-index: 100;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 32px;
  background: rgba(255, 255, 255, 0.2);
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-text {
  flex: 1;
}

.app-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  color: white;
}

.app-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 4px 0 0 0;
  font-weight: 400;
}

.document-info {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.doc-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: white;
  line-height: 1.4;
  word-break: break-word;
}

.doc-meta {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.action-section {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.primary-btn {
  width: 100%;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  margin-bottom: 12px;
}

.primary-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.secondary-btn {
  width: 100%;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.secondary-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.btn-icon {
  font-size: 16px;
}

.btn-text {
  font-weight: inherit;
}

.usage-guide {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.guide-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.step-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.step-title {
  font-size: 14px;
  font-weight: 500;
  color: white;
}

.step-desc {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.features-section {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.features-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
}

.feature-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.feature-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.footer-section {
  padding: 20px;
  margin-top: auto;
}

.original-link {
  margin-bottom: 16px;
}

.link-label {
  display: block;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4px;
}

.link-url {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 12px;
  word-break: break-all;
  transition: color 0.3s ease;
}

.link-url:hover {
  color: white;
  text-decoration: underline;
}

.rating-section {
  background: rgba(255, 255, 255, 0.1);
  padding: 12px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.rating-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
   text-align: center;
   padding-bottom: 10px!important;
 
}

.rating-link {
  display: block;
  color: white;
  text-decoration: none;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.rating-link:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* 右侧内容区域 */
.content-area {
  flex: 1;
  margin-left: 320px;
  background: white;
  min-height: 100vh;
}

.document-section {
  background: white;
   min-height: 100vh;
}

.document-header {
  max-width: 840px;
  margin: 0 auto 40px auto;
  text-align: center;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 30px;
}

.document-title {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.document-subtitle {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0;
  font-weight: 400;
}

.document-content {
  max-width: 840px;
  margin: 0 auto;
  font-size: 16px;
  line-height: 1.7;
  color: #2c3e50;
}

/* 文档内容样式优化 */
.document-content img {
  max-width: 100% !important;
  height: auto !important;
  display: block !important;
  margin: 20px auto !important;
}

.document-content .consecutive-images {
  display: flex !important;
  flex-wrap: nowrap !important;
  align-items: flex-start !important;
  gap: 15px !important;
  margin: 25px 0 !important;
  overflow: hidden !important;
}

.document-content .consecutive-images img {
  flex: 1 1 auto !important;
  min-width: 0 !important;
  max-width: none !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
  margin: 0 !important;
  display: block !important;
}

.document-content h1,
.document-content h2,
.document-content h3,
.document-content h4,
.document-content h5,
.document-content h6 {
  margin-top: 40px;
  margin-bottom: 20px;
  line-height: 1.4;
  color: #2c3e50;
  font-weight: 600;
}

.document-content h1 {
  font-size: 32px;
  padding-bottom: 10px;
}

.document-content h2 {
  font-size: 28px;
}

.document-content h3 {
  font-size: 24px;
}

.document-content p {
  margin: 18px 0;
  text-align: justify;
}

.document-content hr {
  margin: 30px 0;
  border: none;
  border-top: 1px solid #e0e0e0;
  height: 1px;
}

.document-content table {
  margin: 25px 0;
  border-collapse: collapse;
  width: 100%;
  border: 1px solid #e0e0e0;
}

.document-content table th,
.document-content table td {
  border: 1px solid #e0e0e0;
  padding: 12px 15px;
  text-align: left;
}

.document-content table th {
  background: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
}

.document-content table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.document-content blockquote {
  margin: 0 !important;
  border-left: 2px solid #bcbfc4;
  color: #646a73;
  padding-left: 10px;
}

p {
  margin: 0 !important;
  padding: 0 !important;
}

a {
  color: #336df4;
  text-decoration: none;
  font-weight: 400;
  &:hover {
    text-decoration: underline;
    text-underline-offset: 4px;
  }
}

u {
  text-decoration: underline;
  text-underline-offset: 4px;
}

.document-content code {
  background: #f4f6f8;
  padding: 4px 8px;
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  font-size: 14px;
  color: #e74c3c;
}

.document-content pre {
  background: #f4f6f8;
  color: #2c3e50;
  padding: 20px;
  border: 1px solid #e0e0e0;
  overflow-x: auto;
  margin: 25px 0;
}

.document-content pre code {
  background: none;
  padding: 0;
  color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sidebar {
    width: 280px;
  }
  
  .content-area {
    margin-left: 280px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    position: relative;
    height: auto;
    min-height: auto;
  }
  
  .content-area {
    margin-left: 0;
  }
  
  .document-section {
    padding: 20px;
  }
  
  .page-container {
    flex-direction: column;
  }
}

/* 打印样式 - 只显示文档内容 */
@media print {
  body {
    display: none;
    background: white !important;
    color: black !important;
  }

  .page-container {
    display: block !important;
  }

  .sidebar {
    display: none !important;
  }

  .content-area {
    margin-left: 0 !important;
  }

  .document-section {
    background: white !important;
    padding: 0 !important;
    box-shadow: none !important;
    border: none !important;
    margin: 0 !important;
  }

  .document-header {
    max-width: none !important;
    margin: 0 0 30px 0 !important;
    border-bottom: 1px solid #ccc !important;
    padding-bottom: 20px !important;
  }

  .document-title {
    color: black !important;
  }

  .document-subtitle {
    color: #666 !important;
  }

  .document-content {
    max-width: none !important;
    margin: 0 !important;
    color: black !important;
  }
}
