<template>
  <Teleport to="body">
    <div v-if="visible" class="fixed inset-0 z-50 flex items-center justify-center px-4" @click="handleBackdropClick">
      <!-- 背景遮罩 -->
      <div class="absolute inset-0 bg-black bg-opacity-50 transition-opacity duration-300"
        :class="{ 'opacity-0': !visible }" />

      <!-- 成功消息框 -->
      <div class="relative bg-white rounded-2xl shadow-2xl p-8 max-w-sm w-full transform transition-all duration-300"
        :class="{ 'scale-95 opacity-0': !visible, 'scale-100 opacity-100': visible }">
        <!-- 成功图标 -->
        <div class="flex justify-center mb-6">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
        </div>

        <!-- 消息内容 -->
        <div class="text-center">
          <h3 class="text-xl font-semibold text-gray-900 mb-2">
            {{ title }}
          </h3>
          <p class="text-gray-600 mb-6">
            {{ message }}
          </p>

          <!-- 确认按钮 -->
          <button @click="handleConfirm"
            class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
            确定
          </button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'

interface Props {
  title?: string
  message: string
  autoClose?: boolean
  autoCloseDelay?: number
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '操作成功',
  autoClose: true,
  autoCloseDelay: 3000
})

const emit = defineEmits<Emits>()

const visible = ref(false)

// 显示动画
onMounted(async () => {
  await nextTick()
  visible.value = true

  // 自动关闭
  if (props.autoClose) {
    setTimeout(() => {
      handleClose()
    }, props.autoCloseDelay)
  }
})

// 处理关闭
const handleClose = () => {
  visible.value = false
  setTimeout(() => {
    emit('close')
  }, 300) // 等待动画完成
}

// 处理确认
const handleConfirm = () => {
  emit('confirm')
  handleClose()
}

// 处理背景点击
const handleBackdropClick = (e: Event) => {
  if (e.target === e.currentTarget) {
    handleClose()
  }
}
</script>