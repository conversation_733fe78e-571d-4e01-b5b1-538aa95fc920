<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { authClient, authUtils } from '../utils/auth-client'
import SuccessMessage from './components/SuccessMessage.vue'

// 响应式数据
const formData = reactive({
  email: '<EMAIL>',
  code: '000000'
})

const state = reactive({
  isLoading: false,
  isSendingCode: false,
  codeSent: false,
  countdown: 0,
  showSuccess: false,
  successMessage: '',
  errors: {
    email: '',
    code: '',
    general: ''
  }
})

// 计算属性
const isEmailValid = computed(() => {
  return authUtils.isValidEmail(formData.email)
})

const isFormValid = computed(() => {
  return isEmailValid.value && authUtils.isValidCode(formData.code)
})

const countdownText = computed(() => {
  return state.countdown > 0 ? `${state.countdown}s后重新发送` : '发送验证码'
})

// 清除错误信息
const clearErrors = () => {
  state.errors.email = ''
  state.errors.code = ''
  state.errors.general = ''
}

// 验证邮箱格式
const validateEmail = () => {
  if (!formData.email) {
    state.errors.email = '请输入邮箱地址'
    return false
  }
  if (!isEmailValid.value) {
    state.errors.email = '请输入有效的邮箱地址'
    return false
  }
  state.errors.email = ''
  return true
}

// 验证验证码格式
const validateCode = () => {
  if (!formData.code) {
    state.errors.code = '请输入验证码'
    return false
  }
  if (!authUtils.isValidCode(formData.code)) {
    state.errors.code = '验证码必须是6位数字'
    return false
  }
  state.errors.code = ''
  return true
}

// 倒计时功能
const startCountdown = () => {
  state.countdown = 60
  const timer = setInterval(() => {
    state.countdown--
    if (state.countdown <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 发送验证码
const sendCode = async () => {
  if (!validateEmail()) return

  clearErrors()
  state.isSendingCode = true

  try {
    const result = await authClient.sendCode(formData.email)

    if (result.success) {
      state.codeSent = true
      startCountdown()
      console.log('验证码发送成功:', result.message)
    } else {
      state.errors.general = result.message || '发送验证码失败，请重试'
    }
  } catch (error) {
    state.errors.general = authUtils.formatError(error)
  } finally {
    state.isSendingCode = false
  }
}

// 验证码登录
const login = async () => {
  if (!validateEmail() || !validateCode()) return

  clearErrors()
  state.isLoading = true

  try {
    const result = await authClient.login(formData.email, formData.code)

    if (result.success) {
      console.log('登录成功:', result.message)
      console.log('用户信息:', result.user)

      // 这里可以跳转到主页面或者触发其他登录后的操作
      // window.location.href = '/main' 或者使用路由跳转
      showSuccessMessage('登录成功！')

      // 可以在这里添加登录成功后的跳转逻辑
      // setTimeout(() => {
      //   window.close() // 关闭登录窗口
      // }, 1500)

    } else {
      state.errors.general = result.message || '登录失败，请检查验证码是否正确'
    }
  } catch (error) {
    state.errors.general = authUtils.formatError(error)
  } finally {
    state.isLoading = false
  }
}

// 显示成功消息
const showSuccessMessage = (message: string) => {
  state.successMessage = message
  state.showSuccess = true
}

// 处理成功消息关闭
const handleSuccessClose = () => {
  state.showSuccess = false
  state.successMessage = ''
}

// 处理成功消息确认
const handleSuccessConfirm = () => {
  handleSuccessClose()
  // 关闭登录窗口
  window.close()
}

// 处理表单提交
const handleSubmit = (e: Event) => {
  e.preventDefault()
  if (!state.codeSent) {
    sendCode()
  } else {
    login()
  }
}

// 处理验证码输入（只允许数字）
const handleCodeInput = (e: Event) => {
  const target = e.target as HTMLInputElement
  const value = target.value.replace(/\D/g, '').slice(0, 6)
  formData.code = value
  target.value = value
}

// 重置表单状态
const resetForm = () => {
  state.codeSent = false
  formData.code = ''
  state.countdown = 0
  clearErrors()
}
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="w-full max-w-md">
      <!-- Logo 区域 -->
      <div class="text-center mb-8">
        <div class="w-16 h-16 bg-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
            </path>
          </svg>
        </div>
        <h1 class="text-2xl font-bold text-gray-900 mb-2">飞书转存专家登录</h1>
        <p class="text-gray-600">请输入邮箱地址获取验证码</p>
      </div>

      <!-- 登录表单 -->
      <div class="bg-white rounded-2xl shadow-xl p-8">
        <form @submit="handleSubmit" class="space-y-6">
          <!-- 邮箱输入 -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
              邮箱地址
            </label>
            <div class="relative">
              <input id="email" v-model="formData.email" type="email" :disabled="state.codeSent"
                class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors disabled:bg-gray-50 disabled:text-gray-500"
                placeholder="请输入邮箱地址" autocomplete="email" />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                </svg>
              </div>
            </div>
            <p v-if="state.errors.email" class="mt-2 text-sm text-red-600">
              {{ state.errors.email }}
            </p>
          </div>

          <!-- 验证码输入 -->
          <div v-if="state.codeSent">
            <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
              验证码
            </label>
            <div class="relative">
              <input id="code" v-model="formData.code" type="text" maxlength="6" @input="handleCodeInput"
                class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-center text-lg tracking-widest"
                placeholder="请输入6位验证码" autocomplete="one-time-code" />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
            </div>
            <p v-if="state.errors.code" class="mt-2 text-sm text-red-600">
              {{ state.errors.code }}
            </p>
          </div>

          <!-- 错误提示 -->
          <div v-if="state.errors.general" class="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex">
              <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clip-rule="evenodd" />
              </svg>
              <p class="ml-3 text-sm text-red-700">{{ state.errors.general }}</p>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="space-y-4">
            <!-- 发送验证码按钮 -->
            <button v-if="!state.codeSent" type="submit" :disabled="!isEmailValid || state.isSendingCode"
              class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
              <svg v-if="state.isSendingCode" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
              </svg>
              {{ state.isSendingCode ? '发送中...' : '发送验证码' }}
            </button>

            <!-- 登录按钮和重新发送 -->
            <div v-else class="space-y-3">
              <button type="submit" :disabled="!isFormValid || state.isLoading"
                class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                <svg v-if="state.isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg>
                {{ state.isLoading ? '登录中...' : '登录' }}
              </button>

              <!-- 重新发送验证码 -->
              <div class="text-center">
                <button type="button" @click="sendCode" :disabled="state.countdown > 0 || state.isSendingCode"
                  class="text-sm text-indigo-600 hover:text-indigo-500 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors">
                  {{ countdownText }}
                </button>
              </div>

              <!-- 更换邮箱 -->
              <div class="text-center">
                <button type="button" @click="resetForm()"
                  class="text-sm text-gray-600 hover:text-gray-500 transition-colors">
                  更换邮箱地址
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- 帮助信息 -->
      <div class="mt-6 text-center text-sm text-gray-600">
        <p>验证码有效期为5分钟</p>
        <p class="mt-1">如未收到邮件，请检查垃圾邮件箱</p>
      </div>
    </div>

    <!-- 成功提示组件 -->
    <SuccessMessage v-if="state.showSuccess" :message="state.successMessage" @close="handleSuccessClose"
      @confirm="handleSuccessConfirm" />
  </div>
</template>
