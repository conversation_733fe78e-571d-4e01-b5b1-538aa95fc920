<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { authClient, authUtils } from '../utils/auth-client'
 
// 响应式数据
const formData = reactive({
  email: '<EMAIL>',
  code: ''
})

const state = reactive({
  isLoading: false,
  isSendingCode: false,
  codeSent: false,
  countdown: 0,
  loginSuccess: false,
  userInfo: null as any,
  isInitializing: true,
  errors: {
    email: '',
    code: '',
    general: ''
  }
})

// 计算属性
const isEmailValid = computed(() => {
  return authUtils.isValidEmail(formData.email)
})

const isFormValid = computed(() => {
  return isEmailValid.value && authUtils.isValidCode(formData.code)
})

const countdownText = computed(() => {
  return state.countdown > 0 ? `${state.countdown}s后重新发送` : '发送验证码'
})

// 检查用户登录状态
const checkLoginStatus = async () => {
  try {
    const result = await authClient.getProfile()
    
    if (result.success && result.user) {
      // 用户已登录，显示用户信息
      state.userInfo = result.user
      state.loginSuccess = true
      console.log('用户已登录:', result.user)
    }
  } catch (error) {
    // 用户未登录或token无效，保持在登录页面
    console.log('用户未登录:', error)
  } finally {
    state.isInitializing = false
  }
}

// 组件挂载时检查登录状态
onMounted(() => {
  checkLoginStatus()
})

// 清除错误信息
const clearErrors = () => {
  state.errors.email = ''
  state.errors.code = ''
  state.errors.general = ''
}

// 验证邮箱格式
const validateEmail = () => {
  if (!formData.email) {
    state.errors.email = '请输入邮箱地址'
    return false
  }
  if (!isEmailValid.value) {
    state.errors.email = '请输入有效的邮箱地址'
    return false
  }
  state.errors.email = ''
  return true
}

// 验证验证码格式
const validateCode = () => {
  if (!formData.code) {
    state.errors.code = '请输入验证码'
    return false
  }
  if (!authUtils.isValidCode(formData.code)) {
    state.errors.code = '验证码必须是6位数字'
    return false
  }
  state.errors.code = ''
  return true
}

// 倒计时功能
const startCountdown = () => {
  state.countdown = 60
  const timer = setInterval(() => {
    state.countdown--
    if (state.countdown <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 发送验证码
const sendCode = async () => {
  if (!validateEmail()) return

  clearErrors()
  state.isSendingCode = true

  try {
    const result = await authClient.sendCode(formData.email)

    if (result.success) {
      state.codeSent = true
      startCountdown()
      console.log('验证码发送成功:', result.message)
    } else {
      state.errors.general = result.message || '发送验证码失败，请重试'
    }
  } catch (error) {
    state.errors.general = authUtils.formatError(error)
  } finally {
    state.isSendingCode = false
  }
}

// 验证码登录
const login = async () => {
  if (!validateEmail() || !validateCode()) return

  clearErrors()
  state.isLoading = true

  try {
    const result = await authClient.login(formData.email, formData.code)

    if (result.success) {
      console.log('登录成功:', result.message)
      console.log('用户信息:', result.user)
      
      // 保存用户信息并显示成功页面
      state.userInfo = result.user || { email: formData.email, loginTime: new Date().toLocaleString() }
      state.loginSuccess = true

    } else {
      state.errors.general = result.message || '登录失败，请检查验证码是否正确'
    }
  } catch (error) {
    state.errors.general = authUtils.formatError(error)
  } finally {
    state.isLoading = false
  }
}

// 退出登录
const logout = () => {
  state.loginSuccess = false
  state.userInfo = null
  state.codeSent = false
  formData.code = ''
  state.countdown = 0
  clearErrors()
}

// 处理表单提交
const handleSubmit = (e: Event) => {
  e.preventDefault()
  if (!state.codeSent) {
    sendCode()
  } else {
    login()
  }
}

// 处理验证码输入（只允许数字）
const handleCodeInput = (e: Event) => {
  const target = e.target as HTMLInputElement
  const value = target.value.replace(/\D/g, '').slice(0, 6)
  formData.code = value
  target.value = value
}

// 重置表单状态
const resetForm = () => {
  state.codeSent = false
  formData.code = ''
  state.countdown = 0
  clearErrors()
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      
      <!-- 初始化加载状态 -->
      <div v-if="state.isInitializing" class="bg-white shadow rounded-lg p-6">
        <div class="text-center py-8">
          <div class="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p class="text-sm text-gray-500">检查登录状态...</p>
        </div>
      </div>
      
      <!-- 登录成功页面 -->
      <div v-else-if="state.loginSuccess" class="bg-white shadow rounded-lg p-6">
        <!-- 头部 -->
        <div class="text-center pb-6 border-b border-gray-200">
          <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h2 class="text-xl font-semibold text-gray-900">登录成功</h2>
          <p class="text-sm text-gray-500 mt-1">欢迎使用飞书转存专家</p>
        </div>

        <!-- 用户信息 -->
        <div class="py-6 space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-500">邮箱地址</span>
            <span class="text-sm text-gray-900">{{ state.userInfo?.email || formData.email }}</span>
          </div>
          
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-500">登录时间</span>
            <span class="text-sm text-gray-900">{{ state.userInfo?.loginTime || new Date().toLocaleString() }}</span>
          </div>
          
          <div v-if="state.userInfo?.name" class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-500">用户名称</span>
            <span class="text-sm text-gray-900">{{ state.userInfo.name }}</span>
          </div>
          
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-500">状态</span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              已登录
            </span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="pt-6 border-t border-gray-200 space-y-3">
          <button type="button" @click="logout"
            class="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md text-sm font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
            切换账号
          </button>
        </div>
      </div>

      <!-- 登录表单 -->
      <div v-else>
        <!-- 头部 -->
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
              </path>
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-gray-900">飞书转存专家</h2>
          <p class="mt-2 text-sm text-gray-600">请使用邮箱验证码登录</p>
        </div>

        <!-- 表单 -->
        <div class="bg-white shadow rounded-lg p-6 mt-8">
          <form @submit="handleSubmit" class="space-y-6">
            <!-- 邮箱输入 -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700">
                邮箱地址
              </label>
              <input id="email" v-model="formData.email" type="email" :disabled="state.codeSent"
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
                placeholder="请输入邮箱地址" />
              <p v-if="state.errors.email" class="mt-1 text-sm text-red-600">
                {{ state.errors.email }}
              </p>
            </div>

            <!-- 验证码输入 -->
            <div v-if="state.codeSent">
              <label for="code" class="block text-sm font-medium text-gray-700">
                验证码
              </label>
              <input id="code" v-model="formData.code" type="text" maxlength="6" @input="handleCodeInput"
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-center text-lg tracking-widest"
                placeholder="请输入6位验证码" />
              <p v-if="state.errors.code" class="mt-1 text-sm text-red-600">
                {{ state.errors.code }}
              </p>
            </div>

            <!-- 错误提示 -->
            <div v-if="state.errors.general" class="bg-red-50 border border-red-200 rounded-md p-3">
              <p class="text-sm text-red-700">{{ state.errors.general }}</p>
            </div>

            <!-- 提交按钮 -->
            <div>
              <button v-if="!state.codeSent" type="submit" 
                :disabled="!isEmailValid || state.isSendingCode"
                class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                {{ state.isSendingCode ? '发送中...' : '发送验证码' }}
              </button>

              <button v-else type="submit" 
                :disabled="!isFormValid || state.isLoading"
                class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                {{ state.isLoading ? '登录中...' : '登录' }}
              </button>
            </div>

            <!-- 辅助操作 -->
            <div v-if="state.codeSent" class="flex justify-between items-center text-sm">
              <button type="button" @click="sendCode" 
                :disabled="state.countdown > 0 || state.isSendingCode"
                class="text-blue-600 hover:text-blue-500 disabled:text-gray-400 disabled:cursor-not-allowed">
                {{ countdownText }}
              </button>
              <button type="button" @click="resetForm"
                class="text-gray-600 hover:text-gray-500">
                更换邮箱
              </button>
            </div>
          </form>
        </div>

        <!-- 帮助信息 -->
        <div class="text-center text-xs text-gray-500 mt-6 space-y-1">
          <p>验证码有效期为5分钟</p>
          <p>如未收到邮件，请检查垃圾邮件箱</p>
        </div>
      </div>
    </div>
  </div>
</template>
