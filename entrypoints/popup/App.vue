<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { authStorage } from '../utils/auth-client';
// 移除直接导入的API调用
// import { recordUserAction } from '../utils/download-stats';

// 版本号响应式变量
const manifest = ref<any>({});

// 登录状态相关
const isLoggedIn = ref<boolean>(true);
const isCheckingAuth = ref<boolean>(true);
const userInfo = ref<any>(null);

// 检查登录状态
const checkAuthStatus = async (): Promise<boolean> => {
  try {
    const loggedIn = await authStorage.isLoggedIn();
    isLoggedIn.value = loggedIn;

    // 如果已登录，获取用户信息（包括邮箱）
    if (loggedIn) {
      try {
        const userInfoData = await authStorage.getUserInfo();
        userInfo.value = userInfoData;
      } catch (error) {
        console.warn('获取用户信息失败:', error);
        userInfo.value = { email: null, hasToken: loggedIn };
      }
    } else {
      userInfo.value = null;
    }

    return loggedIn;
  } catch (error) {
    console.error('检查登录状态失败:', error);
    isLoggedIn.value = false;
    return false;
  } finally {
    isCheckingAuth.value = false;
  }
};

// 需要登录的操作列表 - 所有功能都需要登录

// 发送消息到后台脚本
const sendMessageToBg = async (action: string): Promise<void> => {
  try {
    // 判断是否登录，如果未登录且操作需要登录，则跳转到登录页面
    if (!isLoggedIn.value) {
      // 引导用户登录
      await browser.runtime.sendMessage({ action: 'login' });
      window.close();
      return;
    }

    // 发送消息到background script，包含统计信息
    await browser.runtime.sendMessage({
      action,
      needStats: isLoggedIn.value
    });
    // window.close();

  } catch (error) {
    console.error('操作失败:', error);
  }
};

// 导出处理函数
const handleExportPdf = () => sendMessageToBg("exportPdf");
const handleExportWord = () => sendMessageToBg("exportWord");
const handleExportMd = () => sendMessageToBg("exportMd");
const handleCopyToFeishu = () => sendMessageToBg("copyToFeishu");
const handleExportHtml = () => sendMessageToBg("exportHtml");
const handleBreakCopy = () => sendMessageToBg("preview2");

// 登录处理
const handleLogin = () => {
  sendMessageToBg("login");
};

// 登出处理
const handleLogout = async () => {
  try {
    await authStorage.clearAllUserData();
    await checkAuthStatus(); // 重新检查登录状态
  } catch (error) {
    console.error('登出失败:', error);
  }
};

// 获取扩展版本号和检查登录状态
onMounted(async () => {
  try {
    manifest.value = browser.runtime.getManifest();
  } catch (error) {
    console.error('获取版本号失败:', error);
  }

  // // 检查登录状态
  // await checkAuthStatus();

  // // 监听用户数据变化
  // authStorage.onUserDataChange(async (data) => {
  //   await checkAuthStatus();
  // });
});
</script>

<template>
  <div class="w-[330px] bg-white border border-gray-200 shadow-lg">
     <!-- <header class="px-4 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200">
       <div v-if="isCheckingAuth" class="flex items-center justify-center">
        <div class="flex items-center space-x-2">
          <div class="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
          <span class="text-sm text-gray-600">检查登录状态...</span>
        </div>
      </div>

      <div v-else-if="isLoggedIn" class="flex items-center justify-between">
        <div class="flex flex-col space-y-1.5">
          <div class="flex items-center space-x-2">
            <div class="relative">
              <div class="w-2.5 h-2.5 bg-green-500 rounded-full"></div>
              <div class="absolute inset-0 w-2.5 h-2.5 bg-green-500 rounded-full animate-ping opacity-75"></div>
            </div>
            <span class="text-sm font-medium text-gray-800">已登录</span>
          </div>
          <div v-if="userInfo?.email"
            class="text-xs text-gray-600 truncate max-w-[160px] bg-white/50 px-2 py-1 rounded-md">
            {{ userInfo.email }}
          </div>
        </div>
        <button
          class="text-xs text-gray-500 hover:text-red-600 hover:bg-white/50 px-2 py-1 rounded-md transition-all duration-200 cursor-pointer"
          @click="handleLogout">
          登出
        </button>
      </div>

      <div v-else>
        <button
          class="w-full text-sm bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2.5 rounded-lg cursor-pointer hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-sm"
          @click="handleLogin">
          <div class="flex items-center justify-center space-x-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
            </svg>
            <span>登录</span>
          </div>
        </button>
      </div>
    </header> -->

    <!-- Content -->
    <div class="p-4">
      <div class="grid grid-cols-3 gap-3">
        <!-- PDF Button -->
        <button @click="handleBreakCopy" :class="[
          'cursor-pointer flex flex-col items-center justify-center p-3 text-center border rounded-lg transition-all duration-200 group h-20',
          !isLoggedIn
            ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
            : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        ]">
          <img src="@/assets/pdf.svg" alt="PDF" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900 flex items-center">
            破解复制
            <svg v-if="!isLoggedIn" class="w-3 h-3 ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 6 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </button>
        <!-- PDF Button -->
        <button @click="handleExportPdf" :class="[
          'cursor-pointer flex flex-col items-center justify-center p-3 text-center border rounded-lg transition-all duration-200 group h-20',
          !isLoggedIn
            ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
            : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        ]">
          <img src="@/assets/pdf.svg" alt="PDF" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900 flex items-center">
            PDF
            <svg v-if="!isLoggedIn" class="w-3 h-3 ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 6 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </button>

        <button @click="handleExportMd" :class="[
          'cursor-pointer flex flex-col items-center justify-center p-3 text-center border rounded-lg transition-all duration-200 group h-20',
          !isLoggedIn
            ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
            : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        ]">
          <img src="@/assets/md.svg" alt="Markdown" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900 flex items-center">
            Markdown
            <svg v-if="!isLoggedIn" class="w-3 h-3 ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </button>

        <!-- Word Button -->
        <button @click="handleExportWord" :class="[
          'cursor-pointer flex flex-col items-center justify-center p-3 text-center border rounded-lg transition-all duration-200 group h-20',
          !isLoggedIn
            ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
            : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        ]">
          <img src="@/assets/word.svg" alt="Word" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900 flex items-center">
            Word
            <svg v-if="!isLoggedIn" class="w-3 h-3 ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </button>

        <button @click="handleExportHtml" :class="[
          'cursor-pointer flex flex-col items-center justify-center p-3 text-center border rounded-lg transition-all duration-200 group h-20',
          !isLoggedIn
            ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
            : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        ]">
          <img src="@/assets/html.svg" alt="HTML" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900 flex items-center">
            HTML
            <svg v-if="!isLoggedIn" class="w-3 h-3 ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </button>

        <button @click="handleCopyToFeishu" :class="[
          'cursor-pointer flex flex-col items-center justify-center p-3 text-center border rounded-lg transition-all duration-200 group h-20',
          !isLoggedIn
            ? 'border-orange-200 bg-orange-50 hover:bg-orange-100'
            : 'border-gray-200 hover:bg-gray-50 hover:border-gray-300'
        ]">
          <img src="@/assets/feishu.svg" alt="Feishu" class="w-6 h-6 mb-1">
          <div class="text-xs font-medium text-gray-900 flex items-center">
            转存
            <svg v-if="!isLoggedIn" class="w-3 h-3 ml-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </button>
      </div>

    

      <!-- 底部信息区域 -->
      <div class="mt-4 pt-4 border-t border-gray-100">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"></path>
              <path
                d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z">
              </path>
            </svg>
            <span class="text-sm text-gray-700 font-medium">QQ群</span>
            <span class="text-sm text-blue-600 font-mono bg-blue-50 px-2 py-0.5 rounded">741683982</span>
          </div>
          <div class="flex items-center space-x-1">
            <svg class="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                clip-rule="evenodd"></path>
            </svg>
            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded font-mono">
              v{{ manifest?.version || '-' }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>